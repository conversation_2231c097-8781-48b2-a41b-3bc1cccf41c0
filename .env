VITE_APP_VERSION = v9.2.0
GENERATE_SOURCEMAP = false

## Public URL
PUBLIC_URL =
VITE_APP_BASE_NAME =

## Backend API URL
# VITE_APP_API_URL=https://mock-data-api-nextjs.vercel.app/
VITE_APP_API_URL= http://localhost:8080
# VITE_APP_API_URL= http://************:8080
 
## Google Map Key
VITE_APP_GOOGLE_MAPS_API_KEY=AIzaSyAXv4RQK39CskcIB8fvM1Q7XCofZcLxUXw

## Map Box 
VITE_APP_MAPBOX_ACCESS_TOKEN=pk.eyJ1IjoicmFrZXNoLW5ha3JhbmkiLCJhIjoiY2xsNjNkZm0yMGhvcDNlb3phdjF4dHlzeiJ9.ps6azYbr7M3rGk_QTguMEQ

## Firebase - Google Auth 
VITE_APP_FIREBASE_API_KEY=
VITE_APP_FIREBASE_AUTH_DOMAIN=
VITE_APP_FIREBASE_PROJECT_ID=
VITE_APP_FIREBASE_STORAGE_BUCKET=
VITE_APP_FIREBASE_MESSAGING_SENDER_ID=
VITE_APP_FIREBASE_APP_ID=
VITE_APP_FIREBASE_MEASUREMENT_ID=

## AWS
VITE_APP_AWS_POOL_ID=
VITE_APP_AWS_APP_CLIENT_ID=

## JWT
VITE_APP_JWT_SECRET_KEY=ikRgjkhi15HJiU78-OLKfjngiu= 'ced35672f556c0ade93da500e7b579a9bf2543f6499c082463dbf1fd87768f93'
VITE_APP_JWT_TIMEOUT=1d

## Auth0
VITE_APP_AUTH0_CLIENT_ID=
VITE_APP_AUTH0_DOMAIN=
