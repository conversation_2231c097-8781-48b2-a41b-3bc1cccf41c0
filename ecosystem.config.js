module.exports = {
  apps: [{
    name: 'ats-frontend-dev',
    script: 'npm',
    args: 'run start:dev',
    watch: true,
    env: {
      NODE_ENV: 'development',
      PORT: 3000
    }
  }, {
    name: 'ats-frontend-beta',
    script: 'npm',
    args: 'run start:beta',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'beta',
      PORT: 3009
    }
  }, {
    name: 'ats-frontend-prod',
    script: 'npm',
    args: 'run start:prod',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3010
    }
  }]
}; 