import React from 'react';
import { TextField } from '@mui/material';
 
const NameTextField = ({ onChange, ...props }) => {
  const handleChange = (event) => {
    const newValue = event.target.value.replace(/(^\s+|[^a-zA-Z\s])/g, "");
    onChange(newValue);
  };
 
 
  return (
    <TextField
      type="text"
      {...props}
      onChange={handleChange}
    />
  );
};
 
export default NameTextField;