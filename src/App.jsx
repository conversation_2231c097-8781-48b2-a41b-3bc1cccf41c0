import { RouterProvider } from 'react-router-dom';

// project import
import router from 'routes';
import ThemeCustomization from 'themes';

import Locales from 'components/Locales';
// import RTLLayout from 'components/RTLLayout';
import ScrollTop from 'components/ScrollTop';
import Snackbar from 'components/@extended/Snackbar';

// auth-provider
import { JWTProvider as AuthProvider } from 'contexts/JWTContext';
import { RBACProvider } from 'pages/permissions/RBACContext';
// ==============================|| APP - THEME, ROUTER, LOCAL  ||============================== //

// export default function App() {
//   return (
//     <ThemeCustomization>
//       {/* <RTLLayout> */}
//       <Locales>
//         <ScrollTop>
//           <AuthProvider>
//             <>
//             <RBACProvider>
//               <RouterProvider router={router} />
//               <Snackbar />
//               </RBACProvider>
//             </>
//           </AuthProvider>
//         </ScrollTop>
//       </Locales>
//       {/* </RTLLayout> */}
//     </ThemeCustomization>
//   );
// }
export default function App() {
  return (
    <ThemeCustomization>
      <Locales>
        <ScrollTop>
          {/* ✅ AuthProvider wraps RBACProvider to ensure JWTContext loads first */}
          <AuthProvider>
            <RBACProvider>
              <RouterProvider router={router} />
              <Snackbar />
            </RBACProvider>
          </AuthProvider>
        </ScrollTop>
      </Locales>
    </ThemeCustomization>
  );
}