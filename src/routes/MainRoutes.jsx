import { lazy } from 'react';

// project-imports
import Loadable from 'components/Loadable';
import DashboardLayout from 'layout/Dashboard';
import PagesLayout from 'layout/Pages';
import SimpleLayout from 'layout/Simple';
import { SimpleLayoutType } from 'config';
import Jobseeker from 'pages/admin-setup-panel/security/jobseeker';
import PasswordConfiguration from 'pages/admin-setup-panel/security/password-configuration';
import EditPasswordConfigurationPage from 'pages/admin-setup-panel/security/password-configuration/EditPasswordConfigurationPage';
import QuickAppsAccessConfig from 'pages/admin-setup-panel/security/quick-apps-access-config';
import Roles from 'pages/admin-setup-panel/security/roles';
import AddRole from 'pages/admin-setup-panel/security/roles/add';
import EditRole from 'pages/admin-setup-panel/security/roles/edit';
import GeneratePDF from 'pages/admin-setup-panel/security/roles/generate-pdf';


const MaintenanceError = Loadable(lazy(() => import('pages/maintenance/error/404')));
const MaintenanceError500 = Loadable(lazy(() => import('pages/maintenance/error/500')));
const MaintenanceUnderConstruction = Loadable(lazy(() => import('pages/maintenance/under-construction/under-construction')));
const MaintenanceComingSoon = Loadable(lazy(() => import('pages/maintenance/coming-soon/coming-soon')));

const AppContactUS = Loadable(lazy(() => import('pages/contact-us')));
// render - sample page
const SamplePage = Loadable(lazy(() => import('pages/extra-pages/sample-page')));
const AdminSetupPage = Loadable(lazy(() => import('pages/admin-setup')));

const AdminSetup_panel =Loadable(lazy(() => import('pages/admin-setup-panel')));

const Client = Loadable(lazy(() => import('pages/client')));
const JobRequest = Loadable(lazy(() => import('pages/jobrequest')));
// const JObRequestPage = Loadable(lazy(() => import('pages/maintenance/JobRequestadd.jsx')));
const JObRequisition = Loadable(lazy(() => import('pages/job-requisition')));
const Leads = Loadable(lazy(() => import('pages/leads')));
const Vendors = Loadable(lazy(() => import('pages/vendors')));
const Placements = Loadable(lazy(() => import('pages/placements/index')));
const Talent_Bench  = Loadable(lazy(() => import('pages/talent-bench')));



// const Applyjob = Loadable(lazy(() => import('pages/applicants')));
const Candidateform = Loadable(lazy(() => import('pages/candidateform')));
const Graduateform = Loadable(lazy(() => import('pages/graduateform')));
const Profilepage = Loadable(lazy(() => import('pages/profile')));
const ClientPage = Loadable(lazy(() => import('pages/client-page')));
const UsersPage = Loadable(lazy(() => import('pages/userPermissions/users/index.jsx')));
const RolesPage = Loadable(lazy(() => import('pages/userPermissions/roles/index.jsx')));
const LevelHierarchy = Loadable(lazy(() => import('pages/userPermissions/levelheirarchy/index.jsx')))

const AddJobRequest = Loadable(lazy(() => import('pages/jobrequest/add/navigator/AddJobRequestNav')));
const EditJobRequest = Loadable(lazy(() => import('pages/jobrequest/edit/navigator/EditJobRequestNav')));
const ViewJobRequest =Loadable(lazy(() => import('pages/jobrequest/viewJobRequest')));



const AddPlacements = Loadable(lazy(()=> import('pages/placements/addplacements')))
const EditPlacements = Loadable(lazy(()=> import('pages/placements/EditPlacement')))
const ViewPlacements = Loadable(lazy(() => import("pages/placements/view/ViewPlacements")))
const AddTableBench = Loadable(lazy(() => import('pages/talent-bench/addtablebench')))
const EditTableBench = Loadable(lazy(() => import('pages/talent-bench/EditTableBench')))

const AddJobRequisition = Loadable(lazy(() => import('pages/job-requisition/add/navigate/AddJobRequisitionNav')))
const EditJobRequisition = Loadable(lazy(()=> import('pages/job-requisition/edit/navigate/EditJobRequisitionNav')))
const ViewJobRequisition = Loadable(lazy (() => import('pages/job-requisition/View/navigate/ViewJobRequisition')))

const AddLeads = Loadable(lazy(()=> import('pages/leads/add/addleads')))
const EditLeads = Loadable(lazy(()=> import('pages/leads/edit/editleads')))
// const ViewLeads = Loadable(lazy(()=> import('pages/leads/ViewLeadNav')))
const ViewLeadsInfo = Loadable(lazy(()=> import('pages/leads/view/ViewLeadsInfo')))

const AddVendors = Loadable(lazy(()=> import('pages/vendors/add/navigation/addvendors')))
const EditVendors = Loadable(lazy(()=> import('pages/vendors/edit/navigation/EditVendors')))
const ViewVendors = Loadable(lazy(()=> import('pages/vendors/view/navigation/ViewVendor')))

const AddRolesPage =  Loadable(lazy(() => import('pages/userPermissions/roles/addRoles.jsx')));
const EditRolesPage =  Loadable(lazy(() => import('pages/userPermissions/roles/editRoles.jsx')));
const AddUsersPage =  Loadable(lazy(() => import('pages/userPermissions/users/AddUsers.jsx')));
const EditUsersPage =  Loadable(lazy(() => import('pages/userPermissions/users/EditUsers.jsx')));

const AddClient=Loadable(lazy(() => import('pages/client-page/add/navigate/AddClientNav.jsx')));
const EditClient=Loadable(lazy(() => import('pages/client-page/edit/navigate/EditClientNew.jsx')));
const ViewClient=Loadable(lazy(() => import('pages/client-page/view/navigate/ViewClientNav')));
const ClientGroup=Loadable(lazy(() => import('pages/client-page/client-group/ClientGroup.jsx')));
const ClientApprovals=Loadable(lazy(() => import('pages/client-page/approvals/ClientApprovals.jsx')));


const ApplicantsPage = Loadable(lazy(() => import('pages/applicant-new/index')));
const AddApplicant = Loadable(lazy(() => import('pages/applicant-new/add/navigate/AddApplicantNav')));
const EditApplicant = Loadable(lazy(() => import('pages/applicant-new/edit/navigate/EditApplicantNav')));
const ViewApplicant = Loadable(lazy(() => import('pages/applicant-new/view/navigate/ViewApplicantNav')));


const JobTemplatePage = Loadable(lazy(() => import('pages/job-template/index'))); 
const JobTemplateAdd = Loadable(lazy(() => import('pages/job-template/AddJobTemplate.jsx'))); 
const JobTemplateEdit = Loadable(lazy(() => import('pages/job-template/EditJobTemplate.jsx'))); 
const JobTemplateView = Loadable(lazy(() => import('pages/job-template/ViewJobTemplate.jsx'))); 

const AdminSetupAPISettings = Loadable(lazy(() => import('pages/admin-setup/APISettings/index')));
const AdminSetupReminders = Loadable(lazy(() => import('pages/admin-setup/Reminders/index')));
const AdminSetupClients = Loadable(lazy(() => import('pages/admin-setup/Clients')));
const AdminSetupCampus = Loadable(lazy(() => import('pages/admin-setup/Campus/index')));
const AdminSetupJobTemplate = Loadable(lazy(() => import('pages/admin-setup/JobTemplate/index')));
const AdminSetupJobPosting = Loadable(lazy(() => import('pages/admin-setup/JobPosting/index')));
const AdminSetupJobRequisition = Loadable(lazy(() => import('pages/admin-setup/JobRequisition/index')));
const AdminSetupApplicants = Loadable(lazy(() => import('pages/admin-setup/Applicants/index')));
const AdminSetupPlacement = Loadable(lazy(() => import('pages/admin-setup/Placement/index')));
const AdminSetupTalentBench = Loadable(lazy(() => import('pages/admin-setup/TalentBench/index')));
const AdminSetupVendor = Loadable(lazy(() => import('pages/admin-setup/Vendor/index')));
const AdminSetupLeads = Loadable(lazy(() => import('pages/admin-setup/Leads/index')));
const AdminSetupSecurity = Loadable(lazy(() => import('pages/admin-setup/Security/index')));
const AdminSetupTextToHire = Loadable(lazy(() => import('pages/admin-setup/TextToHire/index')));
const AdminSetupGlobalSettings = Loadable(lazy(() => import('pages/admin-setup/GlobalSettings/index')));
const AdminSetupOrganization = Loadable(lazy(() => import('pages/admin-setup/Organization/index')));
const AdminSetupWhatsApp = Loadable(lazy(() => import('pages/admin-setup/WhatsApp/index')));



// *******Admin-Setup-ClientPage********

const AdminSetupClientLookUps = Loadable(lazy(() => import('pages/admin-setup/Clients/Client_Lookups')))
const AdminSetupClientContactVisibility = Loadable(lazy(() => import('pages/admin-setup/Clients/Client_Contact_Visibility')))

const ClientLookUps = Loadable(lazy(() => import('pages/admin-setup-panel/client/client-lookups')))
const ClientContactVisibility = Loadable(lazy(() => import('pages/admin-setup-panel/client/client-contact-visibility')))
const ClientDocumentTypes = Loadable(lazy(() => import('pages/admin-setup-panel/client/client-document-types')))
const ClientTabOrder = Loadable(lazy(() => import('pages/admin-setup-panel/client/client-tab-order')))
const ClientSettings = Loadable(lazy(() => import('pages/admin-setup-panel/client/client-settings')))

//Leads
const CallType = Loadable(lazy(() => import('pages/admin-setup-panel/leads/call-type')))
const Statuses = Loadable(lazy(() => import('pages/admin-setup-panel/leads/statuses')))
const Sources = Loadable(lazy(() => import('pages/admin-setup-panel/leads/sources')))
const Settings = Loadable(lazy(() => import('pages/admin-setup-panel/leads/settings')))


//Vendors
const VendorContactStatuses = Loadable(lazy(() => import('pages/admin-setup-panel/vendors/vendor-contact-statuses')))
const VendorDocumenttypes = Loadable(lazy(() => import('pages/admin-setup-panel/vendors/vendor-document-types')))
const VendorSettings = Loadable(lazy(() => import('pages/admin-setup-panel/vendors/vendor-settings')))

//Bench
const BenchAgeConfiguration = Loadable(lazy(() => import('pages/admin-setup-panel/bench/bench-age-configuration')))
const TalentBenchLookups = Loadable(lazy(() => import('pages/admin-setup-panel/bench/talent-bench-lookups')))
const TalentBenchSettings = Loadable(lazy(() => import('pages/admin-setup-panel/bench/talent-bench-settings')))
const TalentBenchTabOrder = Loadable(lazy(() => import('pages/admin-setup-panel/bench/talent-bench-tab-order')))





// Jobs
const CompanyOverview = Loadable(lazy(() => import('pages/admin-setup-panel/JobPosting/companyOverview/CompanyOverView')))
const DisqualificationReason = Loadable(lazy(() => import('pages/admin-setup-panel/JobPosting/DisqualificationReason')))
const JobPostingSettings = Loadable(lazy(() => import('pages/admin-setup-panel/JobPosting/JobPostingSettings/JobPostingSettings')))
const InterViewSetting = Loadable(lazy(() => import('pages/admin-setup-panel/JobPosting/InterViewSetting/InterviewSetting')))
const JobPostingLookups = Loadable(lazy(() => import('pages/admin-setup-panel/JobPosting/JobPostingLookups/JobPostingLookups')))
const JobPostingStatuses = Loadable(lazy(() => import('pages/admin-setup-panel/JobPosting/JobPostingStatuses/JobPostingStatusesTabs')))
const JobPostingSummary = Loadable(lazy(() => import('pages/admin-setup-panel/JobPosting/JobPostingSummary/JobPostingSummary')))
const KeyWorkMasking = Loadable(lazy(() => import('pages/admin-setup-panel/JobPosting/KeyWorkMasking/KeyWorkMasking')))
const Priorities = Loadable(lazy(() => import('pages/admin-setup-panel/JobPosting/Priorities/Priorities')))


// Campus
const Campus = Loadable(lazy(() => import('pages/admin-setup-panel/Campus/index')))


// Placements
const CostSheets = Loadable(lazy(() => import('pages/admin-setup-panel/Placements/CostSheet/index')));
const CostSheetDetails = Loadable(lazy(() => import('pages/admin-setup-panel/Placements/CostSheetDetails/index')));
const ApprovalFlow = Loadable(lazy(() => import('pages/admin-setup-panel/Placements/CostSheetDetails/ApprovalFlow/index')));
const OverheadExpenses = Loadable(lazy(() => import('pages/admin-setup-panel/Placements/OverheadExpenses/index')));
const NetMarginFormula = Loadable(lazy(() => import('pages/admin-setup-panel/Placements/NetMarginFormula/index')));
const PayBreakupSettings = Loadable(lazy(() => import('pages/admin-setup-panel/Placements/PayBreakupSettings/index')));
const PlacementLookups = Loadable(lazy(() => import('pages/admin-setup-panel/Placements/PlacementLookups/index')));
const PlacementSettings = Loadable(lazy(() => import('pages/admin-setup-panel/Placements/PlacementSettings/index')));
const ProfileMarginSettings = Loadable(lazy(() => import('pages/admin-setup-panel/Placements/ProfileMarginSettings/index')));

//organization
const BillingDetails = Loadable(lazy(() => import('pages/admin-setup-panel/organization/billing-details')));
const BusinessUnits = Loadable(lazy(() => import('pages/admin-setup-panel/organization/business-units')));
const EditBusinessUnitPage = Loadable(lazy(() => import('pages/admin-setup-panel/organization/business-units/editbusinessunitdialog')));
const ActivitiesPage = Loadable(lazy(() => import('pages/admin-setup-panel/organization/business-units/activitiespage')));
const NotificationsConfigPage = Loadable(lazy(() => import('pages/admin-setup-panel/organization/business-units/notificationsconfigdialog')));
const CustomLabelsPage = Loadable(lazy(() => import('pages/admin-setup-panel/organization/business-units/custom-labels')));
const CustomEmailTemplates = Loadable(lazy(() => import('pages/admin-setup-panel/organization/custom-email-templates')));
const EmailTemplates = Loadable(lazy(() => import('pages/admin-setup-panel/organization/email-templates')));
const Hierarchy = Loadable(lazy(() => import('pages/admin-setup-panel/organization/hierarchy')));
const ListViewSorting = Loadable(lazy(() => import('pages/admin-setup-panel/organization/list-view-sorting')));
const OrganizationLookups = Loadable(lazy(() => import('pages/admin-setup-panel/organization/organization-lookups')));
const OrganizationSettings = Loadable(lazy(() => import('pages/admin-setup-panel/organization/settings')));
const SSLClientCertificate = Loadable(lazy(() => import('pages/admin-setup-panel/organization/ssl-client-certificate')));
const SubmissionTabsDisplay = Loadable(lazy(() => import('pages/admin-setup-panel/organization/submission-tabs-display')));
const DataBackup = Loadable(lazy(() => import('pages/admin-setup-panel/organization/data-backup')));
const TargetSettings = Loadable(lazy(() => import('pages/admin-setup-panel/organization/target-settings')));



//Applicants

const ApplicantsLookups = Loadable(lazy(() => import('pages/admin-setup-panel/applicants/applicantsLookups/index')));
const ApplicantSettings = Loadable(lazy(() => import('pages/admin-setup-panel/applicants/applicantSettings/index')));
const ApplicantSources = Loadable(lazy(() => import('pages/admin-setup-panel/applicants/applicantSources/index')));
const ApplicantsStatuses = Loadable(lazy(() => import('pages/admin-setup-panel/applicants/ApplicantsStatuses/index')));
const ApplicantTabOrder = Loadable(lazy(() => import('pages/admin-setup-panel/applicants/applicantTabOrder/index')));
const ApplicantConfiguration = Loadable(lazy(() => import('pages/admin-setup-panel/applicants/applicantConfiguration/index')));
const NoticePeriods = Loadable(lazy(() => import('pages/admin-setup-panel/applicants/noticePeriods/index')));
const ApplicationStatuses = Loadable(lazy(() => import('pages/admin-setup-panel/applicants/applicationStatuses/index')));
const CareerSites = Loadable(lazy(() => import('pages/admin-setup-panel/applicants/careerSites/index')));
const CustomApplicants = Loadable(lazy(() => import('pages/admin-setup-panel/applicants/customApplicants/index')));
const DocumentTypes = Loadable(lazy(() => import('pages/admin-setup-panel/applicants/documentTypes/index')));
const PipelineStatuses = Loadable(lazy(() => import('pages/admin-setup-panel/applicants/pipelineStatuses/index')));
const ProfileDownload = Loadable(lazy(() => import('pages/admin-setup-panel/applicants/profileDownload/index')));
const ResumeBuilder = Loadable(lazy(() => import('pages/admin-setup-panel/applicants/resumeBuilder/index')));
const Submissions = Loadable(lazy(() => import('pages/admin-setup-panel/applicants/submissions/index')));

//security

const AdminPanelAccessControl = Loadable(lazy(() => import('pages/admin-setup-panel/security/access-control')));
const EditJobRequisitionPermissions = Loadable(lazy(() => import('pages/admin-setup-panel/security/access-control/job-requisition/EditJobRequisitionPermissions')));
const EditPlacementPermissions = Loadable(lazy(() => import('pages/admin-setup-panel/security/access-control/placement/EditPlacementPermissions')));
const EditJobPostingPermissions = Loadable(lazy(() => import('pages/admin-setup-panel/security/access-control/job-posting/EditJobPostingPermissions')));
const EdotApplicantPermissions = Loadable(lazy(() => import('pages/admin-setup-panel/security/access-control/applicants/EditApplicantsPermissions')));
const EditVendorsPermissions = Loadable(lazy(() => import('pages/admin-setup-panel/security/access-control/vendors/EditVendorsPermissions')));
const EditJobTemplatePermissions = Loadable(lazy(() => import('pages/admin-setup-panel/security/access-control/job-template/EditJobTemplatePermissions')));
const EditEboardingPermissions = Loadable(lazy(() => import('pages/admin-setup-panel/security/access-control/eboarding/EditEboardingPermissions')));
const EditEvaluationTemplatePermissions = Loadable(lazy(() => import('pages/admin-setup-panel/security/access-control/evaluation-template/EditEvaluationTemplatePermissions')));
const EditEmploymentTestPermissions = Loadable(lazy(() => import('pages/admin-setup-panel/security/access-control/employment-test/EditEmploymentTestPermissions')));
const EditTalentBenchPermissions = Loadable(lazy(() => import('pages/admin-setup-panel/security/access-control/talent-bench/EditTalentBenchPermissions')));
const EditClientPermissions = Loadable(lazy(() => import('pages/admin-setup-panel/security/access-control/client/EditClientPermissions')));
const EditLeadsPermissions = Loadable(lazy(() => import('pages/admin-setup-panel/security/access-control/leads/EditLeadsPermissions')));
const EditCampusesPermissions = Loadable(lazy(() => import('pages/admin-setup-panel/security/access-control/campuses/EditCampusesPermissions')));
const AccessSettings = Loadable(lazy(() => import('pages/admin-setup-panel/security/access-settings')));
const EditBIReportPermissions = Loadable(lazy(() => import('pages/admin-setup-panel/security/bi-report-access-control/EditBIReportPermissions')));
const CustomReportsAccess = Loadable(lazy(() => import('pages/admin-setup-panel/security/bi-report-access-control')));
const EboardingSettings = Loadable(lazy(() => import('pages/admin-setup-panel/security/eboarding-settings')));
const ImportExportHistory = Loadable(lazy(() => import('pages/admin-setup-panel/security/import-export-history')));
const IPConfiguration = Loadable(lazy(() => import('pages/admin-setup-panel/security/ipconfiguration')));

const Teams = Loadable(lazy(() => import('pages/admin-setup-panel/security/teams')));

const AddTeam = Loadable(lazy(() => import('pages/admin-setup-panel/security/teams/add')));
const EditTeam = Loadable(lazy(() => import('pages/admin-setup-panel/security/teams/edit')));

const UserFieldRestriction = Loadable(lazy(() => import('pages/admin-setup-panel/security/user-field-restriction')));

const Users = Loadable(lazy(() => import('pages/admin-setup-panel/security/user')));

// const ApplicantProfileReminder = Loadable(lazy(() => import('pages/admin-setup-panel/reminder/applicantprofile/indexe')));
const ApplicantProfileReminder = Loadable(lazy(() => import('pages/admin-setup-panel/reminder/applicantprofile/indexe.jsx')));
const AddApplicantProfileReminder = Loadable(lazy(() => import('pages/admin-setup-panel/reminder/applicantprofile/AddApplicantProfileReminder')));
const EditApplicantProfileReminder = Loadable(lazy(() => import('pages/admin-setup-panel/reminder/applicantprofile/EditApplicantProfileReminder')));

const ClientReminder = Loadable(lazy(() => import('pages/admin-setup-panel/reminder/client/index')));
const AddClientReminder = Loadable(lazy(() => import('pages/admin-setup-panel/reminder/client/AddClientReminder')));
const EditClientReminder = Loadable(lazy(() => import('pages/admin-setup-panel/reminder/client/EditClientReminder')));

const EmailStatistics = Loadable(lazy(() => import('pages/admin-setup-panel/reminder/email-statistics/index')));
const AddEmailStatistics = Loadable(lazy(() => import('pages/admin-setup-panel/reminder/email-statistics/AddEmailStatistics')));
const EditEmailStatistics = Loadable(lazy(() => import('pages/admin-setup-panel/reminder/email-statistics/EditEmailStatistics')));

const GeneralReminder = Loadable(lazy(() => import('pages/admin-setup-panel/reminder/general/index')));
const AddGeneralReminder = Loadable(lazy(() => import('pages/admin-setup-panel/reminder/general/AddGeneralReminder')));
const EditGeneralReminder = Loadable(lazy(() => import('pages/admin-setup-panel/reminder/general/EditGeneralReminder')));

const JobPostingReminder = Loadable(lazy(() => import('pages/admin-setup-panel/reminder/job-posting/index')));
const AddJobPostingReminder = Loadable(lazy(() => import('pages/admin-setup-panel/reminder/job-posting/AddJobPostingReminder')));
const EditJobPostingReminder = Loadable(lazy(() => import('pages/admin-setup-panel/reminder/job-posting/EditJobPostingReminder')));

const PlacementReminder = Loadable(lazy(() => import('pages/admin-setup-panel/reminder/placements/index')));
const AddPlacementReminder = Loadable(lazy(() => import('pages/admin-setup-panel/reminder/placements/AddPlacementReminder')));
const EditPlacementReminder = Loadable(lazy(() => import('pages/admin-setup-panel/reminder/placements/EditPlacementReminder')));

const TalentBenchReminder = Loadable(lazy(() => import('pages/admin-setup-panel/reminder/talent-bench/index')));
const AddTalentBenchReminder = Loadable(lazy(() => import('pages/admin-setup-panel/reminder/talent-bench/AddTalentBenchReminder')));
const EditTalentBenchReminder = Loadable(lazy(() => import('pages/admin-setup-panel/reminder/talent-bench/EditTalentBenchReminder')));

const VendorReminder = Loadable(lazy(() => import('pages/admin-setup-panel/reminder/vendors/index')));
const AddVendorReminder = Loadable(lazy(() => import('pages/admin-setup-panel/reminder/vendors/AddVendorReminder')));
const EditVendorReminder = Loadable(lazy(() => import('pages/admin-setup-panel/reminder/vendors/EditVendorReminder')));



// Integration
const AllIntegration = Loadable(lazy(() => import('pages/admin-setup-panel/integration/allIntegration/index')))
const ResumeSearchAccount = Loadable(lazy(() => import('pages/admin-setup-panel/integration/resumeSearchAccount/index')))
const JobPostingAccounts = Loadable(lazy(() => import('pages/admin-setup-panel/integration/JobPostingAccounts/index')))
const EmailIntegration = Loadable(lazy(() => import('pages/admin-setup-panel/integration/EmailIntegration/index')))
const IntegrationSettings = Loadable(lazy(() => import('pages/admin-setup-panel/integration/IntegrationSettings/index')))
const MarketPlace = Loadable(lazy(() => import('pages/admin-setup-panel/integration/MarketPlace/index')))
const VSMIntegrations = Loadable(lazy(() => import('pages/admin-setup-panel/integration/VSMIntegrations/index')))
const VSMJobAutoPublish = Loadable(lazy(() => import('pages/admin-setup-panel/integration/VSMJobAutoPublish/index')))
const JobBoardQuestions = Loadable(lazy(() => import('pages/admin-setup-panel/integration/JobBoardQuestions/index')))
const PassiveApplicants = Loadable(lazy(() => import('pages/admin-setup-panel/integration/PassiveApplicants/index')))

// Global Settings
const CustomFields = Loadable(lazy(() => import('pages/admin-setup-panel/globalSettings/CustomFields/index')))
const EmailSettings = Loadable(lazy(() => import('pages/admin-setup-panel/globalSettings/EmailSettings/index')))
const ConcentSettings = Loadable(lazy(() => import('pages/admin-setup-panel/globalSettings/ConcentSettings/index')))
const Lookups = Loadable(lazy(() => import('pages/admin-setup-panel/globalSettings/Lookups/index')))
const MandatoryFields = Loadable(lazy(() => import('pages/admin-setup-panel/globalSettings/MandatoryFields/index')))

//requisition
const JobRequisitionCode = Loadable(lazy(() => import('pages/admin-setup-panel/requisition/job-requisition-code')))
const JobRequisitionLookups = Loadable(lazy(() => import('pages/admin-setup-panel/requisition/job-requisition-lookups')))
const JobRequisitionSettings = Loadable(lazy(() => import('pages/admin-setup-panel/requisition/job-requisition-settings')))
const JobRequisitionWorkflow = Loadable(lazy(() => import('pages/admin-setup-panel/requisition/job-requisition-workflow')))

const AddCareerSite = Loadable(lazy(() => import('pages/admin-setup-panel/applicants/careerSites/CareerSites/AddCareerSiteDialog')));
const EditCareerSite = Loadable(lazy(() => import('pages/admin-setup-panel/applicants/careerSites/CareerSites/EditCareerSiteDialog')));

const AddApplicationStatus = Loadable(lazy(() => import('pages/admin-setup-panel/applicants/applicationStatuses/ApplicationsStatuses/AddApplicationStatusDialog')));
const EditApplicationStatus = Loadable(lazy(() => import('pages/admin-setup-panel/applicants/applicationStatuses/ApplicationsStatuses/EditApplicationStatusDialog')));


// ==============================|| MAIN ROUTES ||============================== //

const MainRoutes = {
  path: '/',
  children: [
    {
      path: '/',
      element: <DashboardLayout />,
      children: [
        {
          path: 'sample-page',
          element: <SamplePage />
        },
        
       
      ]
    },
    {
      path: '/',
      element: <DashboardLayout />,
      children: [
        {
          path: 'client',
          element: <Client />
        }
      ]
    },
    {
      path: '/',
      element: <DashboardLayout />,
      children: [
        {
          path: 'leads',
          element: <Leads />
        },
        // {
        //   path: 'leads/add',
        //   element: <AddLeads />
        // },
        // {
        //   path: 'leads/edit',
        //   element: <EditLeads />
        // },
        // {
        //   path: 'leads/view',
        //   element: <ViewLeads />
        // },
       
      ]
    },
    {
      path: '/',
      element: <DashboardLayout />,
      children: [
        {
          path: 'clientpage',
          element: <ClientPage />
        },

      ]
    },

    {
      path: '/',
      element: <DashboardLayout />,
      children: [
        {
          path: 'admin-setup-panel',
          element: <AdminSetup_panel/>
        },

          /* ---------- Client ---------- */

          {
            path: 'admin-setup-panel/lookups',
            element: <AdminSetupClientLookUps/>
          },
          // {
          //   path: 'admin-setup-panel/client/contact-visibility',
          //   element: <AdminSetupClientContactVisibility/>
          // },
           
            {
            path: 'admin-setup-panel/client/lookups',
            element: <ClientLookUps/>
          },
{
            path: 'admin-setup-panel/client/contact-visibility',
            element: <ClientContactVisibility/>
          },
{
            path: 'admin-setup-panel/client/document-types',
            element: <ClientDocumentTypes/>
          },
{
            path: 'admin-setup-panel/client/tab-order',
            element: <ClientTabOrder/>
          },
{
            path: 'admin-setup-panel/client/settings',
            element: <ClientSettings/>
          },





          /*------------ leads------------- */



          {
            path: 'admin-setup-panel/leads/call-type',
            element: <CallType />
          },{
            path: 'admin-setup-panel/leads/statuses',
            element: <Statuses />
          },{
            path: 'admin-setup-panel/leads/sources',
            element: <Sources />
          },{
            path: 'admin-setup-panel/leads/settings',
            element: <Settings />
          },



/*------------ vendors------------- */



          {
            path: 'admin-setup-panel/vendors/vendor-contact-statuses',
            element: <VendorContactStatuses />
          },{
            path: 'admin-setup-panel/vendors/vendor/vendor-document-types',
            element: <VendorDocumenttypes />
          },{
            path: 'admin-setup-panel/vendors/vendor-settings',
            element: <VendorSettings />
          },



          /*------------ Bench------------- */
          {
            path: 'admin-setup-panel/bench/bench-age-configuration',
            element: <BenchAgeConfiguration />
          },
          {
            path: 'admin-setup-panel/bench/talent-bench-lookups',
            element: <TalentBenchLookups />
          },
          {
            path: 'admin-setup-panel/bench/talent-bench-settings',
            element: <TalentBenchSettings />
          },
          {
            path: 'admin-setup-panel/bench/talent-bench-tab-order',
            element: <TalentBenchTabOrder />
          },




          /*------------ Jobs------------- */
          {
            path: 'admin-setup-panel/jobs/component-overview',
            element: <CompanyOverview />
          },
          {
            path: 'admin-setup-panel/jobs/disqualification-reason',
            element: <DisqualificationReason />
          },
          {
            path: 'admin-setup-panel/jobs/jobposting-setting',
            element: <JobPostingSettings />
          },
          {
            path: 'admin-setup-panel/jobs/interview-setting',
            element: <InterViewSetting />
          },
          {
            path: 'admin-setup-panel/jobs/job-posting-lookups',
            element: <JobPostingLookups />
          },
          {
            path: 'admin-setup-panel/jobs/job-posting-statuses',
            element: <JobPostingStatuses />
          },
          {
            path: 'admin-setup-panel/jobs/job-posting-summary',
            element: <JobPostingSummary />
          },
          {
            path: 'admin-setup-panel/jobs/key-work-masking',
            element: <KeyWorkMasking />
          },
          {
            path: 'admin-setup-panel/jobs/priorities',
            element: <Priorities />
          },




          // Campus
          {
            path: 'admin-setup-panel/campus/lookups',
            element: <Campus />
          },

          // Placements

          {
            path: 'admin-setup-panel/placement',
            element:<CostSheets />
          },
          {
            path: 'admin-setup-panel/placements/cost-sheet/:id',
            element:<CostSheetDetails />
          },
          {
            path: 'admin-setup-panel/placements/cost-sheet/:id/approval-flow',
            element: <ApprovalFlow />
          },
          {
            path:'admin-setup-panel/placements/over-head',
            element:<OverheadExpenses />
          },
          {
            path:'admin-setup-panel/placements/net-margin-formula',
            element:<NetMarginFormula />
          },
          {
            path:'admin-setup-panel/placements/pay-breakup-settings',
            element:<PayBreakupSettings />
          },
          {
            path:'admin-setup-panel/placements/placement-lookups',
            element:<PlacementLookups />
          },
          {
            path:'admin-setup-panel/placements/placement-settings',
            element:<PlacementSettings />
          },
          {
            path:'admin-setup-panel/placements/profile-margin-settings',
            element:<ProfileMarginSettings />
          },


          //organization
          {
            path:'admin-setup-panel/organization/billing-details',
            element:<BillingDetails />
          },
          {
            path:'admin-setup-panel/organization/business-units',
            element:<BusinessUnits />
          },
          {
            path:'admin-setup-panel/organization/business-units/edit',
            element:<EditBusinessUnitPage />
          },
          {
            path:'admin-setup-panel/organization/business-units/activities',
            element:<ActivitiesPage />
          },
          {
            path:'admin-setup-panel/organization/business-units/notifications-config',
            element:<NotificationsConfigPage />
          },
          {
            path:'admin-setup-panel/organization/business-units/custom-labels',
            element:<CustomLabelsPage />
          },
          {
            path:'admin-setup-panel/organization/custom-email-templates',
            element:<CustomEmailTemplates />
          },
          {
            path:'admin-setup-panel/organization/email-templates',
            element:<EmailTemplates />
          },  
          {
            path:'admin-setup-panel/organization/hierarchy',
            element:<Hierarchy />
          },
          {
            path:'admin-setup-panel/organization/list-view-sorting',  
            element:<ListViewSorting />
          },
          {
            path:'admin-setup-panel/organization/organization-lookups',
            element:<OrganizationLookups />
          },
          {
            path:'admin-setup-panel/organization/settings',
            element:<OrganizationSettings />
          },
          {
            path:'admin-setup-panel/organization/ssl-client-certificate', 
            element:<SSLClientCertificate />
          },
          {
            path:'admin-setup-panel/organization/submission-tabs-display',
            element:<SubmissionTabsDisplay />
          },  
          {
            path:'admin-setup-panel/organization/data-backup',
            element:<DataBackup />
          },
          {
            path:'admin-setup-panel/organization/target-settings',  
            element:<TargetSettings />
          },
         
       




          // Applicants
          
          {
            path:'admin-setup-panel/applicants/applicant-lookups',
            element:<ApplicantsLookups />
          },
          {
            path:'admin-setup-panel/applicants/applicant-settings',
            element:<ApplicantSettings />
          },
          {
            path:'admin-setup-panel/applicants/applicant-sources',
            element:<ApplicantSources />
          },
          {
            path:'admin-setup-panel/applicants/applicant-statuses',
            element:<ApplicantsStatuses />
          },
          {
            path:'admin-setup-panel/applicants/applicant-order',
            element:<ApplicantTabOrder />
          },
          {
            path:'admin-setup-panel/applicants/applicant-configuration',
            element:<ApplicantConfiguration />
          },
          {
            path:'admin-setup-panel/applicants/applicant-notice-period',
            element:<NoticePeriods />
          },
          {
            path:'admin-setup-panel/applicants/application-statuses',
            element:<ApplicationStatuses />
          },
          {
            path:'admin-setup-panel/applicants/application-statuses/add',
            element:<AddApplicationStatus />
          },
          {
            path:'admin-setup-panel/applicants/application-statuses/edit',
            element:<EditApplicationStatus />
          },
          {
            path:'admin-setup-panel/applicants/career-sites',
            element:<CareerSites />
          },
          {
            path:'admin-setup-panel/applicants/career-sites/add',
            element:<AddCareerSite />
          },
          {
            path:'admin-setup-panel/applicants/career-sites/edit',
            element:<EditCareerSite />
          },
          {
            path:'admin-setup-panel/applicants/custom-applicants',
            element:<CustomApplicants />
          },
          {
            path:'admin-setup-panel/applicants/document-types',
            element:<DocumentTypes />
          },
          {
            path:'admin-setup-panel/applicants/pipeline-statuses',
            element:<PipelineStatuses />
          },
          {
            path:'admin-setup-panel/applicants/profile-download',
            element:<ProfileDownload />
          },
          {
            path:'admin-setup-panel/applicants/resume-builder',
            element:<ResumeBuilder />
          },
          {
            path:'admin-setup-panel/applicants/submissions',
            element:<Submissions />
          },
          /* ---------- Security ---------- */
          {
            path: 'admin-setup-panel/security/access-control',
            element: <AdminPanelAccessControl/>
          },
          {
            path: 'admin-setup-panel/security/access-control/job-requisition/edit/:roleId',
            element: <EditJobRequisitionPermissions />
          },
          {
            path: 'admin-setup-panel/security/access-control/placement/edit/:roleId',
            element: <EditPlacementPermissions />
          },
          {
            path: 'admin-setup-panel/security/access-control/job-posting/edit/:roleId',
            element: <EditJobPostingPermissions />
          },
          {
            path: 'admin-setup-panel/security/access-control/applicants/edit/:roleId',
            element: <EdotApplicantPermissions />
          },
          {
            path: 'admin-setup-panel/security/access-control/vendors/edit/:roleId',
            element: <EditVendorsPermissions />
          },
          {
            path: 'admin-setup-panel/security/access-control/job-template/edit/:roleId',
            element: <EditJobTemplatePermissions />
          },
          {
            path: 'admin-setup-panel/security/access-control/eboarding/edit/:roleId',
            element: <EditEboardingPermissions />
          },
          {
            path: 'admin-setup-panel/security/access-control/evaluation-template/edit/:roleId',
            element: <EditEvaluationTemplatePermissions />
          },
          {
            path: 'admin-setup-panel/security/access-control/employment-test/edit/:roleId',
            element: <EditEmploymentTestPermissions />
          },
          {
            path: 'admin-setup-panel/security/access-control/talent-bench/edit/:roleId',
            element: <EditTalentBenchPermissions />
          },
          {
            path: 'admin-setup-panel/security/access-control/client/edit/:roleId',
            element: <EditClientPermissions />
          },
          {
            path: 'admin-setup-panel/security/access-control/leads/edit/:roleId',
            element: <EditLeadsPermissions />
          },
          {
            path: 'admin-setup-panel/security/access-control/campuses/edit/:roleId',
            element: <EditCampusesPermissions />

          },



          // Integration
          {
            path: 'admin-setup-panel/integration/all',
            element: <AllIntegration />
          },
          {
          
            path: 'admin-setup-panel/integration/resume-search-account',
            element: <ResumeSearchAccount />
          },
          {
          
            path: 'admin-setup-panel/integration/job-posting-account',
            element: <JobPostingAccounts />
          },
          {
            path: 'admin-setup-panel/integration/email-integration',
            element: <EmailIntegration />
          },
          {
            path: 'admin-setup-panel/integration/integration-settings',
            element: <IntegrationSettings />
          },
          {
            path: 'admin-setup-panel/integration/market-place',
            element: <MarketPlace />
          },
          {
            path: 'admin-setup-panel/integration/vsm-integration',
            element: <VSMIntegrations />
          },
          {
            path: 'admin-setup-panel/integration/vsm-job-auto-publish',
            element: <VSMJobAutoPublish />
          },
          {
            path: 'admin-setup-panel/integration/vsm-job-questions',
            element: <JobBoardQuestions />
          },
          {
            path: 'admin-setup-panel/integration/passive-applicants',
            element: <PassiveApplicants />
          },

          {
            path: 'admin-setup-panel/security/access-control/access-settings',
            element: <AccessSettings />
          },
          {
            path: 'admin-setup-panel/security/BI-report-access-control/edit/:roleId',
            element: <EditBIReportPermissions />
          },
          {
            path: '/admin-setup-panel/security/BI-report-access-control',
            element: <CustomReportsAccess />
          },
          {
            path: 'admin-setup-panel/security/eboarding-settings',
            element: <EboardingSettings />
          },
          {
            path: 'admin-setup-panel/security/import-export-history',
            element: <ImportExportHistory />
          },
          {
            path: 'admin-setup-panel/security/ipconfiguration',
            element: <IPConfiguration />
          },
          {
            path: 'admin-setup-panel/security/jobseeker',
            element: <Jobseeker />
          },
          {
            path: 'admin-setup-panel/security/password-configuration',
            element: <PasswordConfiguration />
          },
          {
            path: 'admin-setup-panel/security/password-configuration/edit',
            element: <EditPasswordConfigurationPage />,
          },
          {
            path: 'admin-setup-panel/security/quick-apps-access-config',
            element: <QuickAppsAccessConfig />
          },
          {
            path: '/admin-setup-panel/security/roles',
            element: <Roles />,
          },
          {
            path: '/admin-setup-panel/security/roles/add',
            element: <AddRole />,
          },
          {
            path: '/admin-setup-panel/security/roles/edit/:id',
            element: <EditRole />,
          },
          {
            path: '/admin-setup-panel/security/roles/generate-pdf',
            element: <GeneratePDF />,
          },
          {
            path: 'admin-setup-panel/security/teams',
            element: <Teams />,
          },
          {
            path: 'admin-setup-panel/security/teams/add',
            element: <AddTeam />,
          },
          {
            path: 'admin-setup-panel/security/teams/edit/:id',
            element: <EditTeam />,
          },
          {
            path: 'admin-setup-panel/security/user-field-restriction',
            element: <UserFieldRestriction />,
          },
          {
            path: 'admin-setup-panel/security/user',
            element: <Users />,
          },
          {
            path: 'admin-setup-panel/global-settings/custom-fields',
            element: <CustomFields />,
          },

          {
            path: 'admin-setup-panel/global-settings/email-settings',
            element: <EmailSettings />,
          },
          {
            path: 'admin-setup-panel/global-settings/concent-settings',
            element: <ConcentSettings />,
          },
          {
            path: 'admin-setup-panel/global-settings/lookups',
            element: <Lookups />,
          },
          {
            path: 'admin-setup-panel/global-settings/mandatory-fields',
            element: <MandatoryFields />,
          },

          /*------------ Requisition------------- */

          {
            path: 'admin-setup-panel/requisition/job-requisition-code',
            element: <JobRequisitionCode />,
          },
          {
            path: 'admin-setup-panel/requisition/job-requisition-lookups',
            element: <JobRequisitionLookups />,
          },
          {
            path: 'admin-setup-panel/requisition/job-requisition-settings',
            element: <JobRequisitionSettings />,
          },
          {
            path: 'admin-setup-panel/requisition/job-requisition-workflow',
            element: <JobRequisitionWorkflow />,
          },









          /*------------ Reminder------------- */
          {
            path: 'admin-setup-panel/reminder/applicantprofile',
            element: <ApplicantProfileReminder />
          },
          {
            path: 'admin-setup-panel/reminder/applicantprofile/add',
            element: <AddApplicantProfileReminder />
          },
          {
            path: 'admin-setup-panel/reminder/applicantprofile/edit/:id',
            element: <EditApplicantProfileReminder />
          },
          {
            path: 'admin-setup-panel/reminder/client',
            element: <ClientReminder />
          },
          {
            path: 'admin-setup-panel/reminder/client/add',
            element: <AddClientReminder />
          },
          {
            path: 'admin-setup-panel/reminder/client/edit/:id',
            element: <EditClientReminder />
          },
          {
            path: 'admin-setup-panel/reminder/email-statistics',
            element: <EmailStatistics />
          },
          {
            path: 'admin-setup-panel/reminder/email-statistics/add',
            element: <AddEmailStatistics />
          },
          {
            path: 'admin-setup-panel/reminder/email-statistics/edit/:id',
            element: <EditEmailStatistics />
          },
          {
            path: 'admin-setup-panel/reminder/general',
            element: <GeneralReminder />
          },
          {
            path: 'admin-setup-panel/reminder/general/add',
            element: <AddGeneralReminder />
          },
          {
            path: 'admin-setup-panel/reminder/general/edit/:id',
            element: <EditGeneralReminder />
          },
          {
            path: 'admin-setup-panel/reminder/job-posting',
            element: <JobPostingReminder />
          },
          {
            path: 'admin-setup-panel/reminder/job-posting/add',
            element: <AddJobPostingReminder />
          },
          {
            path: 'admin-setup-panel/reminder/job-posting/edit/:id',
            element: <EditJobPostingReminder />
          },
          {
            path: 'admin-setup-panel/reminder/placements',
            element: <PlacementReminder />
          },
          {
            path: 'admin-setup-panel/reminder/placements/add',
            element: <AddPlacementReminder />
          },
          {
            path: 'admin-setup-panel/reminder/placements/edit/:id',
            element: <EditPlacementReminder />
          },
          {
            path: 'admin-setup-panel/reminder/talent-bench',
            element: <TalentBenchReminder />
          },
          {
            path: 'admin-setup-panel/reminder/talent-bench/add',
            element: <AddTalentBenchReminder />
          },
          {
            path: 'admin-setup-panel/reminder/talent-bench/edit/:id',
            element: <EditTalentBenchReminder />
          },
          {
            path: 'admin-setup-panel/reminder/vendors',
            element: <VendorReminder />
          },
          {
            path: 'admin-setup-panel/reminder/vendors/add',
            element: <AddVendorReminder />
          },
          {
            path: 'admin-setup-panel/reminder/vendors/edit/:id',
            element: <EditVendorReminder />
          }
        ]
      },
    {
      path: '/',
      element: <DashboardLayout />,
      children: [
        {
          path: 'jobtemplate',
          element: <JobTemplatePage />
        },

      ]
    },
    
    {
      path: '/',
      element: <DashboardLayout />,
      children: [
        {
          path: 'admin-setup',
          element: <AdminSetupPage />
        },
        {
  path: 'admin-setup/apisettings',
  element: <AdminSetupAPISettings />
},
{
  path: 'admin-setup/reminders',
  element: <AdminSetupReminders />
},
{
  path: 'admin-setup/clients',
  element: <AdminSetupClients />
},
{
  path: 'admin-setup/campus',
  element: <AdminSetupCampus />
},
{
  path: 'admin-setup/jobTemplate',
  element: <AdminSetupJobTemplate />
},
{
  path: 'admin-setup/jobPosting',
  element: <AdminSetupJobPosting />
},
{
  path: 'admin-setup/jobrequisition',
  element: <AdminSetupJobRequisition />
},
{
  path: 'admin-setup/applicants',
  element: <AdminSetupApplicants />
},
{
  path: 'admin-setup/placement',
  element: <AdminSetupPlacement />
},
{
  path: 'admin-setup/talentbench',
  element: <AdminSetupTalentBench />
},
{
  path: 'admin-setup/vendor',
  element: <AdminSetupVendor />
},
{
  path: 'admin-setup/leads',
  element: <AdminSetupLeads />
},
{
  path: 'admin-setup/security',
  element: <AdminSetupSecurity />
},
{
  path: 'admin-setup/texttohire',
  element: <AdminSetupTextToHire />
},
{
  path: 'admin-setup/globalsettings',
  element: <AdminSetupGlobalSettings />
},
{
  path: 'admin-setup/organization',
  element: <AdminSetupOrganization />
},
{
  path: 'admin-setup/whatsApp',
  element: <AdminSetupWhatsApp />
}

      ]
    },
    



    {
      path: '/',
      element: <PagesLayout />,
      children: [
        /* ---------- Client ---------- */
        {
          path: 'clientpage/add',          // ➜ Add Client
          element: <AddClient />
        },
        {
          path: 'clientpage/edit',         // ➜ Edit Client
          element: <EditClient />
        },
        {
          path: 'clientpage/view-client',  // ➜ View Client
          element: <ViewClient />
        },
        {
          path: 'clientpage/client-group',  // ➜ View Client
          element: <ClientGroup />
        },
        {
          path: 'clientpage/client-approval',  // ➜ View Client
          element: <ClientApprovals />
        },
    
        /* ---------- Leads ---------- */
        {
          path: 'leads/add',               // ➜ Add Leads
          element: <AddLeads />
        },
        {
          path: 'leads/edit',              // ➜ Edit Leads
          element: <EditLeads />
        },
        // {
        //   path: 'leads/view',              // ➜ View Leads
        //   element: <ViewLeads />
        // },
        {
            path: 'leads/view',              // ➜ View Leads
            element: <ViewLeadsInfo />
        },
    
        /* ---------- Applicant ---------- */
        {
          path: 'applicant/view',          // ➜ View Applicant
          element: <ViewApplicant />
        },
        {
          path: 'applicant/add',           // ➜ Add Applicant
          element: <AddApplicant />
        },
        {
          path: 'applicant/Edit',          // ➜ Edit Applicant
          element: <EditApplicant />
        },
    
        /* ---------- Job Request ---------- */
        {
          path: 'jobrequest/add',          // ➜ Add Job Request
          element: <AddJobRequest />
        },
        {
          path: 'jobrequest/edit',         // ➜ Edit Job Request
          element: <EditJobRequest />
        },
        {
          path: 'jobrequest/view',         // ➜ View Job Request
          element: <ViewJobRequest />
        },
    
        /* ---------- Placements ---------- */
        {
          path: 'placements/add',          // ➜ Add Placements
          element: <AddPlacements />
        },
        {
          path: 'placements/edit',         // ➜ Edit Placements
          element: <EditPlacements />
        },
        {
          path: 'placements/view',         // ➜ View Placements
          element: <ViewPlacements />
        },
    
        /* ---------- Job Requisition ---------- */
        {
          path: 'job_requisition/add',     // ➜ Add Job Requisition
          element: <AddJobRequisition />
        },
        {
          path: 'job_requisition/edit',    // ➜ Edit Job Requisition
          element: <EditJobRequisition />
        },
        {
          path: 'job_requisition/view',    // ➜ View Job Requisition
          element: <ViewJobRequisition />
        },
    
        /* ---------- Vendor ---------- */
        {
          path: '/vendor/add',             // ➜ Add Vendors
          element: <AddVendors />
        },
        {
          path: '/vendor/edit',            // ➜ Edit Vendors
          element: <EditVendors />
        },
        {
          path: '/vendor/view',            // ➜ Edit Vendors
          element: <ViewVendors />
        },
    
        /* ---------- Talent Bench ---------- */
        {
          path: '/talent-bench/add',        // ➜ Add Talent Bench
          element: <AddTableBench />
        },
        {
          path: '/talent-bench/edit',       // ➜ Edit Talent Bench
          element: <EditTableBench />
        },

         /* ---------- Job Template Bench ---------- */
        {
          path: '/jobtemplate/edit', 
          element: <JobTemplateEdit />
        },
        {
          path: '/jobtemplate/add', 
          element: <JobTemplateAdd />
        },
        {
          path: '/jobtemplate/view', 
          element: <JobTemplateView />
        },



        {
          path: '/user-management/add-users', 
          element: <AddUsersPage />
        },
        {
          path: '/user-management/edit-users', 
          element: <EditUsersPage />
        },

        {
          path: '/user-management/add-roles', 
          element: <AddRolesPage />
        },
        {
          path: '/user-management/edit-roles', 
          element: <EditRolesPage />
        },

        




      ]
    },
    




    {
      path: '/',
      element: <DashboardLayout />,
      children: [
        {
          path: 'applicant',
          element: <ApplicantsPage />
        },
        // {
        //   path: 'applicant/view',
        //   element: <ViewApplicant/>
        // },
        // {
        //   path: 'applicant/add',
        //   element: <AddApplicant/>
        // },
        // {
        //   path: 'applicant/Edit',
        //   element: <EditApplicant/>
        // },
        
        
      ]
    },

    {
      path: '/',
      element: <DashboardLayout />,
      children: [
        {
          path: 'placements',
          element: <Placements />
        },
        // {
        //   path: 'placements/add',
        //   element: <AddPlacements />
        // },
        // {
        //   path: 'placements/edit',
        //   element: <EditPlacements />
        // },
        // {
        //   path: 'placements/view',
        //   element: <ViewPlacements />
        // },

        
      ]
    },



    {
      path: '/',
      element: <DashboardLayout />,
      children: [
        {
          path: 'jobrequest',
          element: <JobRequest />
        },
        // {
        //   path: 'jobrequest/add',
        //   element: <AddJobRequest  />
        // },
        // {
        //   path: 'jobrequest/edit',
        //   element: <EditJobRequest  />
        // },
        // {
        //   path: 'jobrequest/view',
        //   element: <ViewJobRequest  />
        // },
      ]
    },
    {
      path: '/',
      element: <DashboardLayout />,
      children: [
        {
          path: 'profile',
          element: <Profilepage />
        }
      ]
    },
    {
      path: '/',
      element: <DashboardLayout />,
      children: [
        {
          path: 'job_requisition',
          element: <JObRequisition />
        },
        // {
        //   path: 'job_requisition/add',
        //   element: <AddJobRequisition  />
        // },
        // {
        //   path: 'job_requisition/edit',
        //   element: <EditJobRequisition  />
        // },
        // {
        //   path: 'job_requisition/view',
        //   element: <ViewJobRequisition  />
        // },
      ]
    },

   
    {
      path: '/',
      element: <DashboardLayout />,
      children: [
        {
          path: '/vendor',
          element: <Vendors />
        },
        // {
        //   path: '/vendor/add',
        //   element: <AddVendors />
        // },
        // {
        //   path: '/vendor/edit',
        //   element: <EditVendors />
        // }
      ]
    },
    {
      path: '/',
      element: <DashboardLayout />,
      children: [
        {
          path: 'talent-bench',
          element: <Talent_Bench />
        },
        // {
        //   path: 'talent-bench/add',
        //   element: <AddTableBench />
        // },
        // {
        //   path: 'talent-bench/edit',
        //   element: <EditTableBench />
        // }
      ]
    },



  
 
    // {
    //   path: '/',
    //   element: <SimpleLayout layout={SimpleLayoutType.SIMPLE} />,
    //   children: [
    //     {
    //       path: 'contact-us',
    //       element: <AppContactUS />
    //     }
    //   ]
    // },
    // {
    //   path: '/',
    //   element: <DashboardLayout />,
    //   children: [
    //     {
    //       path: '/graduate-form',
    //       element: <Graduateform />
    //     }
    //   ]
    // },
    {
      path: '/maintenance',
      element: <PagesLayout />,
      children: [
        {
          path: '404',
          element: <MaintenanceError />
        },
        {
          path: '500',
          element: <MaintenanceError500 />
        },
        {
          path: 'under-construction',
          element: <MaintenanceUnderConstruction />
        },
        {
          path: 'coming-soon',
          element: <MaintenanceComingSoon />
        }
      ]
    },
    {
      path: '/',
      element: <DashboardLayout />,
      children: [
        {
          path: '/user-management/users',
          element: <UsersPage />
        },
        {
          path: '/user-management/add-users', 
          element: <AddUsersPage />
        },
        {
          path: '/user-management/edit-users', 
          element: <EditUsersPage />
        },
      ]
    },
    {
      path: '/',
      element: <DashboardLayout />,
      children: [
        {
          path: '/user-management/roles',
          element: <RolesPage />
        },
        

      ]
    },
    {
      path: '/',
      element: <DashboardLayout />,
      children: [
        {
          path: 'user-management/level-hierarchy',
          element: <LevelHierarchy />
        }
      ]
    },
    {
      path: '*',
      element: <MaintenanceError />
    }
  ]
};

export default MainRoutes;
