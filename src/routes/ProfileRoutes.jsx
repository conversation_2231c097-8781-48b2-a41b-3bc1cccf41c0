import { lazy } from 'react';
import Loadable from 'components/Loadable';
import DashboardLayout from 'layout/Dashboard';

// lazy load profile-related pages
const ViewProfile = Loadable(lazy(() => import('pages/profile')));
// const EditProfile = Loadable(lazy(() => import('pages/edit-profile')));
// const ProfileSettings = Loadable(lazy(() => import('pages/profile-settings')));
// const ProfileSecurity = Loadable(lazy(() => import('pages/profile-security')));

// ==============================|| PROFILE ROUTES ||============================== //

const ProfileRoutes = {
  path: '/',
  element: <DashboardLayout />, // Assuming these pages are part of the dashboard
  children: [
    {
      path: 'view-profile', // Main profile page
      element: <ViewProfile />
    },
    // {
    //   path: 'profile/edit', // Edit profile page
    //   element: <EditProfile />
    // },
    // {
    //   path: 'profile/settings', // Profile settings page
    //   element: <ProfileSettings />
    // },
    // {
    //   path: 'profile/security', // Profile security settings
    //   element: <ProfileSecurity />
    // }
  ]
};

export default ProfileRoutes;
