import React, { useState } from 'react';
import {
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Switch
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';

const additionalFields = [
  'State',
  'Country',
  'Zip Code',
  'Skills',
  'Primary Skills',
  'Additional Comments',
  'Experience',
  'Job Title',
  'Applicant Status',
  'Source',
  'Gender',
  'Race/Ethnicity',
  'Veteran Status',
  'Veteran Type',
  'Referred By',
  'Disability',
  'Expected Pay',
  'Applicant Group',
  'Tax Terms'
];

function AdditionalFieldsAccordion() {
  const [fieldStates, setFieldStates] = useState(
    additionalFields.map((field) => ({
      fieldName: field,
      show: false,
      mandatory: false,
      mandateOnSubmit: false
    }))
  );

  const handleToggle = (index, key) => {
    setFieldStates((prev) =>
      prev.map((row, i) => (i === index ? { ...row, [key]: !row[key] } : row))
    );
  };

  return (
    <Accordion sx={{ mb: 2 }}>
      <AccordionSummary expandIcon={<ExpandMoreIcon />}>
        <Typography fontWeight="bold">Additional Fields</Typography>
      </AccordionSummary>
      <AccordionDetails>
        <TableContainer component={Paper} sx={{ overflow: 'auto' }}>
          <Table stickyHeader>
            <TableHead>
              <TableRow>
                <TableCell sx={{ fontWeight: 'bold' }}>FIELD NAME</TableCell>
                <TableCell sx={{ fontWeight: 'bold' }}>SHOW/HIDE</TableCell>
                <TableCell sx={{ fontWeight: 'bold' }}>MANDATORY</TableCell>
                <TableCell sx={{ fontWeight: 'bold' }}>MANDATE WHILE SUBMITTING</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {fieldStates.map((row, idx) => (
                <TableRow key={row.fieldName}>
                  <TableCell>{row.fieldName}</TableCell>
                  <TableCell>
                    <Switch checked={row.show} onChange={() => handleToggle(idx, 'show')} color="primary" />
                  </TableCell>
                  <TableCell>
                    <Switch checked={row.mandatory} onChange={() => handleToggle(idx, 'mandatory')} color="primary" />
                  </TableCell>
                  <TableCell>
                    <Switch checked={row.mandateOnSubmit} onChange={() => handleToggle(idx, 'mandateOnSubmit')} color="primary" />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </AccordionDetails>
    </Accordion>
  );
}

export default AdditionalFieldsAccordion; 