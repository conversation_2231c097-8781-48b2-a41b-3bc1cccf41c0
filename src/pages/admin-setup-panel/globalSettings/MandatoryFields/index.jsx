import React from 'react';
import { Typography, Grid, Button } from '@mui/material';
import { useForm } from 'react-hook-form';
import CustomDropdownField from 'components/custom-components/CustomDropdownField';
import MainCard from 'components/MainCard';
import PersonalDetailsAccordion from './ApplicantProfile/PersonalDetailsAccordion';
import AdditionalFieldsAccordion from './ApplicantProfile/AdditionalFieldsAccordion';
import EducationDetailsAccordion from './ApplicantProfile/EducationDetailsAccordion';
import BenchDetailsAccordion from './TalentBench/BenchDetailsAccordion';
import PersonalDetailsAccordionTalentBench from './TalentBench/PersonalDetailsAccordionTalentBench';
import EducationDetailsAccordionTalentBench from './TalentBench/EducationDetailsAccordionTalentBench';
import PersonalDetailsAccordionClients from './Clients/PersonalDetailsAccordionClients';
import PersonalDetailsAccordionClientContacts from './ClientContacts/PersonalDetailsAccordionClientContacts';
import PersonalDetailsAccordionVendorContacts from './VendorContacts/PersonalDetailsAccordionVendorContacts';
import PersonalDetailsAccordionVendorJobs from './VendorJobs/PersonalDetailsAccordionVendorJobs';
import PersonalDetailsAccordionLeads from './Leads/PersonalDetailsAccordionLeads';
import PersonalDetailsAccordionLeadContacts from './LeadContacts/PersonalDetailsAccordionLeadContacts';
import PersonalDetailsAccordionUsers from './Users/<USER>';

const modules = [
  'Applicant Profile',
  'Talent Bench',
  'Clients',
  'Client Contacts',
  'Vendor Contacts',
  'Vendor Jobs',
  'Leads',
  'Lead Contacts',
  'Users'
];

function MandatoryFields() {
  const { control, watch } = useForm({
    defaultValues: { module: modules[0] }
  });
  const selectedModule = watch('module');

  return (
    <MainCard
      title="Mandatory Fields"
      secondary={
        <Button variant="contained" size="small" color="primary">
          Save
        </Button>
      }
    >
      <Grid container alignItems="center" spacing={2} mb={2}>
        <Grid item>
          <Typography>Choose Module</Typography>
        </Grid>
        <Grid item xs={12} sm={6} md={4} lg={3}>
          <CustomDropdownField
            name="module"
            control={control}
            placeholder="Choose Module"
            options={modules.map((m) => ({ value: m }))}
            rules={{ required: 'Module is required' }}
            sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
          />
        </Grid>
      </Grid>
      {selectedModule === 'Applicant Profile' && (
        <>
          <PersonalDetailsAccordion />
          <AdditionalFieldsAccordion />
          <EducationDetailsAccordion />
        </>
      )}
      {selectedModule === 'Talent Bench' && (
        <>
          <PersonalDetailsAccordionTalentBench />
          <BenchDetailsAccordion />
          <EducationDetailsAccordionTalentBench />
        </>
      )}
      {selectedModule === 'Clients' && <PersonalDetailsAccordionClients />}
      {selectedModule === 'Client Contacts' && <PersonalDetailsAccordionClientContacts />}
      {selectedModule === 'Vendor Contacts' && <PersonalDetailsAccordionVendorContacts />}
      {selectedModule === 'Vendor Jobs' && <PersonalDetailsAccordionVendorJobs />}
      {selectedModule === 'Leads' && <PersonalDetailsAccordionLeads />}
      {selectedModule === 'Lead Contacts' && <PersonalDetailsAccordionLeadContacts />}
      {selectedModule === 'Users' && <PersonalDetailsAccordionUsers />}
    </MainCard>
  );
}

export default MandatoryFields;
