import React, { useState } from 'react';
import {
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Switch
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';

const staticFields = [
  { fieldName: 'Job Code', mandatory: 'Required' },
  { fieldName: 'Job Title', mandatory: 'Required' },
  { fieldName: 'Vendor', mandatory: 'Required' },
  { fieldName: 'Vendor Manager', mandatory: 'Required' }
];

const toggleFields = [
  'Client Bill Rate/Salary',
  'End Client',
  'Job Owner',
  'City',
  'Country',
  'State',
  'Zip Code',
  'Area Code',
  'Primary Skills',
  'Secondary Skills',
  'Tax Terms',
  'Experience',
  'Duration'
];

function PersonalDetailsAccordionVendorJobs() {
  const [fieldStates, setFieldStates] = useState(
    toggleFields.map((field) => ({
      fieldName: field,
      mandatory: false
    }))
  );

  const handleToggle = (index) => {
    setFieldStates((prev) =>
      prev.map((row, i) => (i === index ? { ...row, mandatory: !row.mandatory } : row))
    );
  };

  return (
    <Accordion sx={{ mb: 2 }}>
      <AccordionSummary expandIcon={<ExpandMoreIcon />}>
        <Typography fontWeight="bold">Personal Details</Typography>
      </AccordionSummary>
      <AccordionDetails>
        <TableContainer component={Paper} sx={{ overflow: 'auto' }}>
          <Table stickyHeader>
            <TableHead>
              <TableRow>
                <TableCell sx={{ fontWeight: 'bold' }}>FIELD NAME</TableCell>
                <TableCell sx={{ fontWeight: 'bold' }}>MANDATORY</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {staticFields.map((row) => (
                <TableRow key={row.fieldName}>
                  <TableCell>{row.fieldName}</TableCell>
                  <TableCell>{row.mandatory}</TableCell>
                </TableRow>
              ))}
              {fieldStates.map((row, idx) => (
                <TableRow key={row.fieldName}>
                  <TableCell>{row.fieldName}</TableCell>
                  <TableCell>
                    <Switch checked={row.mandatory} onChange={() => handleToggle(idx)} color="primary" />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </AccordionDetails>
    </Accordion>
  );
}

export default PersonalDetailsAccordionVendorJobs; 