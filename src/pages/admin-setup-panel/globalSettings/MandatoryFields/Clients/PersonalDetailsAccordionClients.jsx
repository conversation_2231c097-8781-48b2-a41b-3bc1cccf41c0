import React, { useState } from 'react';
import {
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Switch
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';

const staticFields = [
  { fieldName: 'Contact Person Name', show: 'Visible' },
  { fieldName: 'Contact Person Number', show: 'Visible' },
  { fieldName: 'Mobile Number', show: 'Visible' },
  { fieldName: 'Designation', show: 'Visible' },
  { fieldName: 'Email', show: 'Visible' }
];

const toggleFields = [
  'Client Manager'
];

function PersonalDetailsAccordionClients() {
  const [fieldStates, setFieldStates] = useState(
    toggleFields.map((field) => ({
      fieldName: field,
      show: false,
      mandatory: false
    }))
  );
  const [mandatoryStates, setMandatoryStates] = useState(
    staticFields.map(() => false)
  );

  const handleToggle = (index, key) => {
    setFieldStates((prev) =>
      prev.map((row, i) => (i === index ? { ...row, [key]: !row[key] } : row))
    );
  };

  const handleMandatoryStatic = (index) => {
    setMandatoryStates((prev) =>
      prev.map((val, i) => (i === index ? !val : val))
    );
  };

  return (
    <Accordion sx={{ mb: 2 }}>
      <AccordionSummary expandIcon={<ExpandMoreIcon />}>
        <Typography fontWeight="bold">Personal Details</Typography>
      </AccordionSummary>
      <AccordionDetails>
        <TableContainer component={Paper} sx={{ overflow: 'auto' }}>
          <Table stickyHeader>
            <TableHead>
              <TableRow>
                <TableCell sx={{ fontWeight: 'bold' }}>FIELD NAME</TableCell>
                <TableCell sx={{ fontWeight: 'bold' }}>SHOW/HIDE</TableCell>
                <TableCell sx={{ fontWeight: 'bold' }}>MANDATORY</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {staticFields.map((row, idx) => (
                <TableRow key={row.fieldName}>
                  <TableCell>{row.fieldName}</TableCell>
                  <TableCell>{row.show}</TableCell>
                  <TableCell>
                    <Switch checked={mandatoryStates[idx]} onChange={() => handleMandatoryStatic(idx)} color="primary" />
                  </TableCell>
                </TableRow>
              ))}
              {fieldStates.map((row, idx) => (
                <TableRow key={row.fieldName}>
                  <TableCell>{row.fieldName}</TableCell>
                  <TableCell>
                    <Switch checked={row.show} onChange={() => handleToggle(idx, 'show')} color="primary" />
                  </TableCell>
                  <TableCell>
                    <Switch checked={row.mandatory} onChange={() => handleToggle(idx, 'mandatory')} color="primary" />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </AccordionDetails>
    </Accordion>
  );
}

export default PersonalDetailsAccordionClients; 