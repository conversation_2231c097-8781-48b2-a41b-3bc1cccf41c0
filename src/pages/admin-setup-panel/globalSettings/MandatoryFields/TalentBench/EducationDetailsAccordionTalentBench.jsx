import React, { useState } from 'react';
import {
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Switch
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';

const educationFields = [
  'School Name',
  'Degree',
  'Year Completed',
  'Major Study',
  'Minor Study',
  'GPA',
  'School Country',
  'School State',
  'School City'
];

function EducationDetailsAccordionTalentBench() {
  const [fieldStates, setFieldStates] = useState(
    educationFields.map((field) => ({
      fieldName: field,
      show: false
    }))
  );

  const handleToggle = (index) => {
    setFieldStates((prev) =>
      prev.map((row, i) => (i === index ? { ...row, show: !row.show } : row))
    );
  };

  return (
    <Accordion sx={{ mb: 2 }}>
      <AccordionSummary expandIcon={<ExpandMoreIcon />}>
        <Typography fontWeight="bold">Education Details</Typography>
      </AccordionSummary>
      <AccordionDetails>
        <TableContainer component={Paper} sx={{ borderTopLeftRadius: 0, borderTopRightRadius: 0, overflow: 'auto' }}>
          <Table stickyHeader>
            <TableHead>
              <TableRow>
                <TableCell sx={{ fontWeight: 'bold' }}>FIELD NAME</TableCell>
                <TableCell sx={{ fontWeight: 'bold' }}>SHOW/HIDE</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {fieldStates.map((row, idx) => (
                <TableRow key={row.fieldName}>
                  <TableCell>{row.fieldName}</TableCell>
                  <TableCell>
                    <Switch checked={row.show} onChange={() => handleToggle(idx)} color="primary" />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </AccordionDetails>
    </Accordion>
  );
}

export default EducationDetailsAccordionTalentBench; 