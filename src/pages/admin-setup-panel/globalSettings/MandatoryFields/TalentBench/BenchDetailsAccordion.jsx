import React, { useState } from 'react';
import {
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Switch
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';

const staticFields = [
  { fieldName: 'Consultant Name', show: 'Visible', mandatory: '' },
  { fieldName: 'Sales Team Member', show: 'Visible', mandatory: 'Required' },
  { fieldName: 'Bench Status', show: 'Visible', mandatory: 'Required' }
];

const toggleFields = [
  'Priority',
  'Account Manager',
  'Currency',
  'Desired Sell Rate',
  'Pay Frequency Type',
  'Consultant Type',
  'Location Preference',
  'Availability',
  'Comments',
  'Gender',
  'Race/Ethnicity',
  'Veteran Status',
  'Veteran Type',
  'Referred By',
  'Disability',
  'Lead Type',
  'Lead Generated By',
  'Time To Place The Consultant In',
  'Additional Notifiers'
];

function BenchDetailsAccordion() {
  const [fieldStates, setFieldStates] = useState(
    toggleFields.map((field) => ({
      fieldName: field,
      show: false,
      mandatory: false
    }))
  );

  const handleToggle = (index, key) => {
    setFieldStates((prev) =>
      prev.map((row, i) => (i === index ? { ...row, [key]: !row[key] } : row))
    );
  };

  return (
    <Accordion sx={{ mb: 2 }}>
      <AccordionSummary expandIcon={<ExpandMoreIcon />}>
        <Typography fontWeight="bold">Bench Details</Typography>
      </AccordionSummary>
      <AccordionDetails>
        <TableContainer component={Paper} sx={{ overflow: 'auto' }}>
          <Table stickyHeader>
            <TableHead>
              <TableRow>
                <TableCell sx={{ fontWeight: 'bold' }}>FIELD NAME</TableCell>
                <TableCell sx={{ fontWeight: 'bold' }}>SHOW/HIDE</TableCell>
                <TableCell sx={{ fontWeight: 'bold' }}>MANDATORY</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {staticFields.map((row) => (
                <TableRow key={row.fieldName}>
                  <TableCell>{row.fieldName}</TableCell>
                  <TableCell>{row.show}</TableCell>
                  <TableCell>{row.mandatory}</TableCell>
                </TableRow>
              ))}
              {fieldStates.map((row, idx) => (
                <TableRow key={row.fieldName}>
                  <TableCell>{row.fieldName}</TableCell>
                  <TableCell>
                    <Switch checked={row.show} onChange={() => handleToggle(idx, 'show')} color="primary" />
                  </TableCell>
                  <TableCell>
                    <Switch checked={row.mandatory} onChange={() => handleToggle(idx, 'mandatory')} color="primary" />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </AccordionDetails>
    </Accordion>
  );
}

export default BenchDetailsAccordion; 