import React, { useState } from 'react';
import {
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Switch
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';

const staticFields = [
  { fieldName: 'First Name', show: 'Visible', mandatory: 'Required' },
  { fieldName: 'Last Name', show: 'Visible', mandatory: 'Required' },
  { fieldName: 'Email', show: 'Visible', mandatory: 'Required' },
  { fieldName: 'Mobile Number', show: 'Visible', mandatory: 'Required' }
];

const toggleFields = [
  'Date of Birth',
  'SSN',
  'Ownership',
  'Nickname',
  'Middle Name',
  'Alternate Email Address',
  'Home Phone Number',
  'Work Phone Number',
  'Other Phone',
  'Skype Id',
  'Linked In Profile',
  'Facebook Profile',
  'Twitter Profile',
  'Video Reference',
  'Work Authorization',
  'Address',
  'City',
  'Applicant Group',
  'State',
  'Country',
  'Zip Code',
  'Skills',
  'Primary Skills',
  'Experience',
  'Job Title',
  'Source',
  'Technology',
  'Expected Pay',
  'Notice Period',
  'Function',
  'Work Authorization Expiry',
  'Pan Card Number',
  'Clearance',
  'Aadhar Number'
];

function PersonalDetailsAccordionTalentBench() {
  const [fieldStates, setFieldStates] = useState(
    toggleFields.map((field) => ({
      fieldName: field,
      show: false,
      mandatory: false
    }))
  );

  const handleToggle = (index, key) => {
    setFieldStates((prev) =>
      prev.map((row, i) => (i === index ? { ...row, [key]: !row[key] } : row))
    );
  };

  return (
    <Accordion sx={{ mb: 2 }}>
      <AccordionSummary expandIcon={<ExpandMoreIcon />}>
        <Typography fontWeight="bold">Personal Details</Typography>
      </AccordionSummary>
      <AccordionDetails>
        <TableContainer component={Paper} sx={{ overflow: 'auto' }}>
          <Table stickyHeader>
            <TableHead>
              <TableRow>
                <TableCell sx={{ fontWeight: 'bold' }}>FIELD NAME</TableCell>
                <TableCell sx={{ fontWeight: 'bold' }}>SHOW/HIDE</TableCell>
                <TableCell sx={{ fontWeight: 'bold' }}>MANDATORY</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {staticFields.map((row) => (
                <TableRow key={row.fieldName}>
                  <TableCell>{row.fieldName}</TableCell>
                  <TableCell>{row.show}</TableCell>
                  <TableCell>{row.mandatory}</TableCell>
                </TableRow>
              ))}
              {fieldStates.map((row, idx) => (
                <TableRow key={row.fieldName}>
                  <TableCell>{row.fieldName}</TableCell>
                  <TableCell>
                    <Switch checked={row.show} onChange={() => handleToggle(idx, 'show')} color="primary" />
                  </TableCell>
                  <TableCell>
                    <Switch checked={row.mandatory} onChange={() => handleToggle(idx, 'mandatory')} color="primary" />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </AccordionDetails>
    </Accordion>
  );
}

export default PersonalDetailsAccordionTalentBench; 