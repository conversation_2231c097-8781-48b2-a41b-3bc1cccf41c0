import React, { useState } from 'react';
import {
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Switch
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';

const staticFields = [
  { fieldName: 'First Name', mandatory: 'Required' },
  { fieldName: 'Contact Status', mandatory: 'Required' },
  { fieldName: 'Email address', mandatory: 'Required' },
  { fieldName: 'Ownership', mandatory: 'Required' }
];

const toggleFields = [
  'Last Name',
  'Mobile Num',
  'Office Number',
  'Country',
  'State',
  'City',
  'Title',
  'About Contact',
  'Business Card',
  'Linkedin URL',
  'Address1',
  'Address2',
  'Area',
  'Zip Code'
];

function PersonalDetailsAccordionLeadContacts() {
  const [fieldStates, setFieldStates] = useState(
    toggleFields.map((field) => ({
      fieldName: field,
      mandatory: false
    }))
  );

  const handleToggle = (index) => {
    setFieldStates((prev) =>
      prev.map((row, i) => (i === index ? { ...row, mandatory: !row.mandatory } : row))
    );
  };

  return (
    <Accordion sx={{ mb: 2 }}>
      <AccordionSummary expandIcon={<ExpandMoreIcon />}>
        <Typography fontWeight="bold">Personal Details</Typography>
      </AccordionSummary>
      <AccordionDetails>
        <TableContainer component={Paper} sx={{ overflow: 'auto' }}>
          <Table stickyHeader>
            <TableHead>
              <TableRow>
                <TableCell sx={{ fontWeight: 'bold' }}>FIELD NAME</TableCell>
                <TableCell sx={{ fontWeight: 'bold' }}>MANDATORY</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {staticFields.map((row) => (
                <TableRow key={row.fieldName}>
                  <TableCell>{row.fieldName}</TableCell>
                  <TableCell>{row.mandatory}</TableCell>
                </TableRow>
              ))}
              {fieldStates.map((row, idx) => (
                <TableRow key={row.fieldName}>
                  <TableCell>{row.fieldName}</TableCell>
                  <TableCell>
                    <Switch checked={row.mandatory} onChange={() => handleToggle(idx)} color="primary" />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </AccordionDetails>
    </Accordion>
  );
}

export default PersonalDetailsAccordionLeadContacts; 