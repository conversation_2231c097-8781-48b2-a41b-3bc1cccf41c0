import React, { useState } from 'react';
import {
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Switch
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';

const staticFields = [
  { fieldName: 'Lead Name', mandatory: 'Required' },
  { fieldName: 'Website', mandatory: 'Required' },
  { fieldName: 'Ownership', mandatory: 'Required' }
];

const toggleFields = [
  'Contact Number',
  'Lead Source',
  'Lead Status',
  'Country',
  'State',
  'City',
  'Address1',
  'Address2',
  'Zip Code',
  'Metro System',
  'LinkedIn URL',
  'Industry Type',
  'Ownership Type',
  'Number of Employees',
  'Revenue',
  'SIC',
  'NAICS',
  'Number of Locations'
];

function PersonalDetailsAccordionLeads() {
  const [fieldStates, setFieldStates] = useState(
    toggleFields.map((field) => ({
      fieldName: field,
      mandatory: false
    }))
  );

  const handleToggle = (index) => {
    setFieldStates((prev) =>
      prev.map((row, i) => (i === index ? { ...row, mandatory: !row.mandatory } : row))
    );
  };

  return (
    <Accordion sx={{ mb: 2 }}>
      <AccordionSummary expandIcon={<ExpandMoreIcon />}>
        <Typography fontWeight="bold">Personal Details</Typography>
      </AccordionSummary>
      <AccordionDetails>
        <TableContainer component={Paper} sx={{ overflow: 'auto' }}>
          <Table stickyHeader>
            <TableHead>
              <TableRow>
                <TableCell sx={{ fontWeight: 'bold' }}>FIELD NAME</TableCell>
                <TableCell sx={{ fontWeight: 'bold' }}>MANDATORY</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {staticFields.map((row) => (
                <TableRow key={row.fieldName}>
                  <TableCell>{row.fieldName}</TableCell>
                  <TableCell>{row.mandatory}</TableCell>
                </TableRow>
              ))}
              {fieldStates.map((row, idx) => (
                <TableRow key={row.fieldName}>
                  <TableCell>{row.fieldName}</TableCell>
                  <TableCell>
                    <Switch checked={row.mandatory} onChange={() => handleToggle(idx)} color="primary" />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </AccordionDetails>
    </Accordion>
  );
}

export default PersonalDetailsAccordionLeads; 