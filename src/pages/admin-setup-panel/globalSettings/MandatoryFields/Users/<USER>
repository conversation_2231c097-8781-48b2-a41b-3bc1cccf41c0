import React, { useState } from 'react';
import {
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Switch
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';

const staticFields = [
  { fieldName: 'First Name', show: 'Visible', mandatory: 'Required' },
  { fieldName: 'Last Name', show: 'Visible', mandatory: '' },
  { fieldName: 'Nick Name', show: 'Visible', mandatory: 'Required' },
  { fieldName: 'Email', show: 'Visible', mandatory: 'Required' },
  { fieldName: 'Business Units', show: 'Visible', mandatory: 'Required' },
  { fieldName: 'Default Business Unit', show: 'Visible', mandatory: 'Required' },
  { fieldName: 'Reporting To', show: 'Visible', mandatory: 'Required' },
  { fieldName: 'Time Zone', show: 'Visible', mandatory: 'Required' },
  { fieldName: 'Date Of Join', show: 'Visible', mandatory: 'Required' },
  { fieldName: 'Role', show: 'Visible', mandatory: 'Required' }
];

const toggleFields = ['Office Number', 'Mobile Number', 'Employee Id', 'Working Location', 'Date Of Birth', 'Group', 'Team'];

function PersonalDetailsAccordionUsers() {
  const [fieldStates, setFieldStates] = useState(
    toggleFields.map((field) => ({
      fieldName: field,
      show: false,
      mandatory: false
    }))
  );
  const [mandatoryStates, setMandatoryStates] = useState(staticFields.map((f) => f.mandatory === 'Required'));
  const [showStates, setShowStates] = useState(staticFields.map((f) => f.show === 'Visible'));

  const handleToggle = (index, key) => {
    setFieldStates((prev) => prev.map((row, i) => (i === index ? { ...row, [key]: !row[key] } : row)));
  };

  const handleMandatoryStatic = (index) => {
    setMandatoryStates((prev) => prev.map((val, i) => (i === index ? !val : val)));
  };

  const handleShowStatic = (index) => {
    setShowStates((prev) => prev.map((val, i) => (i === index ? !val : val)));
  };

  return (
    <Accordion sx={{ mb: 2 }}>
      <AccordionSummary expandIcon={<ExpandMoreIcon />}>
        <Typography fontWeight="bold">Personal Details</Typography>
      </AccordionSummary>
      <AccordionDetails>
        <TableContainer component={Paper} sx={{ overflow: 'auto' }}>
          <Table stickyHeader>
            <TableHead>
              <TableRow>
                <TableCell sx={{ fontWeight: 'bold' }}>FIELD NAME</TableCell>
                <TableCell sx={{ fontWeight: 'bold' }}>SHOW/HIDE</TableCell>
                <TableCell sx={{ fontWeight: 'bold' }}>MANDATORY</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {staticFields.map((row, idx) => (
                <TableRow key={row.fieldName}>
                  <TableCell>{row.fieldName}</TableCell>
                  <TableCell>{row.show}</TableCell>
                  <TableCell>{row.mandatory}</TableCell>
                </TableRow>
              ))}
              {fieldStates.map((row, idx) => (
                <TableRow key={row.fieldName}>
                  <TableCell>{row.fieldName}</TableCell>
                  <TableCell>
                    <Switch checked={row.show} onChange={() => handleToggle(idx, 'show')} color="primary" />
                  </TableCell>
                  <TableCell>
                    <Switch checked={row.mandatory} onChange={() => handleToggle(idx, 'mandatory')} color="primary" />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </AccordionDetails>
    </Accordion>
  );
}

export default PersonalDetailsAccordionUsers;
