import React, { useState } from 'react';
import { Box, Grid, <PERSON>ton, Link, Typography, Paper, Divider, IconButton, Tooltip, Menu, MenuItem } from '@mui/material';
import { useForm } from 'react-hook-form';
import CustomNameField from 'components/custom-components/CustomNameField';
import CustomDataGrid from 'components/custom-components/CustomDataGrid';
import AllocateCreditsDrawer from './AllocateCreditsDrawer';
import EditAllocationDrawer from './EditAllocationDrawer';
import DeleteConfirmationDialog from './DeleteConfirmationDialog';
import EmailCleanupNotificationDialog from './EmailCleanupNotificationDialog';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import AllocationHistory from './AllocationHistory';

function EmailCleanups() {
  const { control } = useForm({
    defaultValues: {
      search: ''
    }
  });

  const [rows, setRows] = useState([]);
  const [paginationModel, setPaginationModel] = useState({ pageSize: 10, page: 0 });
  const [allocateDrawerOpen, setAllocateDrawerOpen] = useState(false);
  const [editDrawerOpen, setEditDrawerOpen] = useState(false);
  const [selectedAllocation, setSelectedAllocation] = useState(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [showHistory, setShowHistory] = useState(false);
  const [notificationDialogOpen, setNotificationDialogOpen] = useState(false);

  const handleAllocateDrawerOpen = () => {
    setAllocateDrawerOpen(true);
  };

  const handleAllocateDrawerClose = () => {
    setAllocateDrawerOpen(false);
  };

  const handleEditDrawerOpen = (row) => {
    setSelectedAllocation(row);
    setEditDrawerOpen(true);
  };

  const handleEditDrawerClose = () => {
    setEditDrawerOpen(false);
    setSelectedAllocation(null);
  };

  const handleDeleteClick = (row) => {
    setSelectedAllocation(row);
    setDeleteDialogOpen(true);
  };

  const handleDeleteDialogClose = () => {
    setDeleteDialogOpen(false);
    setSelectedAllocation(null);
  };

  const handleDeleteConfirm = () => {
    setRows(rows.filter((row) => row.id !== selectedAllocation.id));
    handleDeleteDialogClose();
  };

  const handleAddCredit = (data) => {
    const newAllocation = {
      id: rows.length + 1,
      userName: data.user,
      email: `${data.user.toLowerCase().replace(' ', '.')}@example.com`,
      phone: '************',
      allocatedCredits: data.credits,
      availableCredits: data.credits,
      allocatedBy: 'Admin',
      allocatedOn: new Date().toLocaleDateString()
    };
    setRows([...rows, newAllocation]);
  };

  const handleUpdateCredit = (updatedData) => {
    setRows(rows.map((row) => (row.id === updatedData.id ? { ...row, ...updatedData } : row)));
  };

  const handleNotificationDialogOpen = () => {
    setNotificationDialogOpen(true);
  };

  const handleNotificationDialogClose = () => {
    setNotificationDialogOpen(false);
  };

  const ActionCell = ({ row }) => {
    const [anchorEl, setAnchorEl] = useState(null);
    const open = Boolean(anchorEl);

    const handleMenuClick = (event) => {
      setAnchorEl(event.currentTarget);
    };

    const handleMenuClose = () => {
      setAnchorEl(null);
    };

    const onEdit = () => {
      handleEditDrawerOpen(row);
      handleMenuClose();
    };

    const onDelete = () => {
      handleDeleteClick(row);
      handleMenuClose();
    };

    return (
      <>
        <Tooltip title="Actions">
          <IconButton onClick={handleMenuClick} size="small">
            <MoreVertIcon />
          </IconButton>
        </Tooltip>
        <Menu anchorEl={anchorEl} open={open} onClose={handleMenuClose}>
          <MenuItem onClick={onEdit}>Edit</MenuItem>
          <MenuItem onClick={onDelete}>Delete</MenuItem>
        </Menu>
      </>
    );
  };

  const columns = [
    { field: 'userName', headerName: 'USER NAME', flex: 1 },
    { field: 'email', headerName: 'EMAIL', flex: 1 },
    { field: 'phone', headerName: 'PHONE', flex: 1 },
    { field: 'allocatedCredits', headerName: 'ALLOCATED CREDITS', flex: 1, type: 'number' },
    { field: 'availableCredits', headerName: 'AVAILABLE CREDITS', flex: 1, type: 'number' },
    { field: 'allocatedBy', headerName: 'ALLOCATED BY', flex: 1 },
    { field: 'allocatedOn', headerName: 'ALLOCATED ON', flex: 1 },
    {
      field: 'actions',
      headerName: 'ACTIONS',
      flex: 1,
      sortable: false,
      renderCell: (params) => <ActionCell row={params.row} />
    }
  ];

  if (showHistory) {
    return <AllocationHistory onBack={() => setShowHistory(false)} />;
  }

  return (
    <Box sx={{pt:2}}>
      <Grid container spacing={4} alignItems="center" sx={{ mb: 2 }}>
        <Grid item xs={12} sm={6} md={3}>
          <CustomNameField name="search" control={control} placeholder="Search" fullWidth />
        </Grid>
        <Grid
          item
          xs={12}
          sm={6}
          md={9}
          sx={{
            display: 'flex',
            flexDirection: { xs: 'column', sm: 'row' },
            justifyContent: { sm: 'flex-end' },
            alignItems: { xs: 'flex-start', sm: 'center' },
            gap: 2
          }}
        >
          <Link onClick={() => setShowHistory(true)} component="button" underline="hover" sx={{ color: '#1890ff', cursor: 'pointer' }}>
            Allocation History
          </Link>
          <Link onClick={handleNotificationDialogOpen} component="button" underline="hover" sx={{ color: '#1890ff', cursor: 'pointer' }}>
            Email Cleanup Notification
          </Link>
          <Button variant="contained" size="small" color="primary" sx={{ textTransform: 'none' }} onClick={handleAllocateDrawerOpen}>
            + Allocate Credits
          </Button>
        </Grid>
      </Grid>

      <Paper variant="outlined" sx={{ p: 2, mb: 2, backgroundColor: '#f5f5f5' }}>
        <Grid container spacing={2} alignItems="center" textAlign="center">
          <Grid item xs>
            <Typography variant="body2" color="textSecondary">
              Cleanup Credits
            </Typography>
            <Typography variant="h6" sx={{ color: 'green' }}>
              5
            </Typography>
          </Grid>
          <Divider orientation="vertical" flexItem />
          <Grid item xs>
            <Typography variant="body2" color="textSecondary">
              Allocated Credits
            </Typography>
            <Typography variant="h6">&nbsp;</Typography>
          </Grid>
          <Divider orientation="vertical" flexItem />
          <Grid item xs>
            <Typography variant="body2" color="textSecondary">
              Available Credits
            </Typography>
            <Typography variant="h6">5</Typography>
          </Grid>
        </Grid>
      </Paper>

      <CustomDataGrid
        rows={rows}
        columns={columns}
        paginationModel={paginationModel}
        onPaginationModelChange={setPaginationModel}
        rowCount={rows.length}
        sx={{
          '& .MuiDataGrid-columnHeaders': {
            backgroundColor: '#f5f5f5',
            fontWeight: 'bold'
          }
        }}
      />
      <AllocateCreditsDrawer open={allocateDrawerOpen} onClose={handleAllocateDrawerClose} onAdd={handleAddCredit} />
      <EditAllocationDrawer
        open={editDrawerOpen}
        onClose={handleEditDrawerClose}
        onUpdate={handleUpdateCredit}
        allocationData={selectedAllocation}
      />
      <DeleteConfirmationDialog open={deleteDialogOpen} onClose={handleDeleteDialogClose} onConfirm={handleDeleteConfirm} />
      <EmailCleanupNotificationDialog open={notificationDialogOpen} onClose={handleNotificationDialogClose} />
    </Box>
  );
}

export default EmailCleanups; 