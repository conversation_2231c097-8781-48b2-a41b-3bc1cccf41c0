import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Typography,
  FormGroup,
  FormControlLabel,
  Checkbox,
  Button,
  Grid
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';

const EmailCleanupNotificationDialog = ({ open, onClose }) => {
  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        Email Clean Up Notification
        <IconButton aria-label="close" onClick={onClose}>
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      <DialogContent dividers>
        <Typography variant="body1" gutterBottom>
          Email Cleanup notifications should be sent to the following recipients?
        </Typography>
        <FormGroup row>
          <FormControlLabel control={<Checkbox />} label="Administrator" />
          <FormControlLabel control={<Checkbox defaultChecked />} label="Initiator" />
          <FormControlLabel control={<Checkbox />} label="Specific Users" />
        </FormGroup>
      </DialogContent>
      <DialogActions>
        <Grid container justifyContent="flex-end" spacing={1}>
          <Grid item>
            <Button variant="outlined" onClick={onClose} size="small">
              Cancel
            </Button>
          </Grid>
          <Grid item>
            <Button variant="contained" color="primary" onClick={onClose} size="small">
              Save
            </Button>
          </Grid>
        </Grid>
      </DialogActions>
    </Dialog>
  );
};

export default EmailCleanupNotificationDialog; 