import React from 'react';
import { Drawer, Box, Typography, IconButton, Grid, Button } from '@mui/material';
import { Close } from '@mui/icons-material';
import { useForm } from 'react-hook-form';
import CustomInputLabel from 'components/custom-components/CustomInputLabel';
import CustomNameField from 'components/custom-components/CustomNameField';
import CustomDropdownField from 'components/custom-components/CustomDropdownField';

const defaultFormValues = {
  user: '',
  credits: ''
};

// Dummy user options, replace with your actual data
const userOptions = [
  { value: 'Nagaraju', label: 'Nagaraju' },
  { value: '<PERSON><PERSON>h<PERSON>nmuri', label: '<PERSON><PERSON><PERSON><PERSON>' },
  { value: 'Radhika G', label: 'Radhika G' }
];

const AllocateCreditsDrawer = ({ open, onClose, onAdd }) => {
  const { control, handleSubmit, reset } = useForm({
    defaultValues: defaultFormValues
  });

  React.useEffect(() => {
    if (open) {
      reset(defaultFormValues);
    }
  }, [open, reset]);

  const onSubmit = (data) => {
    onAdd(data);
    onClose();
  };

  return (
    <Drawer anchor="right" open={open} onClose={onClose}>
      <Box
        sx={{
          width: { xs: '90vw', sm: 500 },
          display: 'flex',
          flexDirection: 'column',
          height: '100%'
        }}
        role="presentation"
      >
        <Box sx={{ flexShrink: 0, borderBottom: 1, borderColor: 'divider', p: 2 }}>
          <Grid container justifyContent="space-between" alignItems="center">
            <Grid item>
              <Typography variant="h5">Allocate Credits</Typography>
            </Grid>
            <Grid item>
              <IconButton onClick={onClose} size="small">
                <Close />
              </IconButton>
            </Grid>
          </Grid>
        </Box>

        <Box sx={{ flexGrow: 1, overflowY: 'auto', p: 3 }}>
          <form onSubmit={handleSubmit(onSubmit)}>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <CustomInputLabel required>User</CustomInputLabel>
                <CustomDropdownField
                  name="user"
                  control={control}
                  options={userOptions}
                  placeholder="Select"
                  fullWidth
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              </Grid>
              <Grid item xs={12}>
                <CustomInputLabel required>Credits</CustomInputLabel>
                <CustomNameField control={control} name="credits" fullWidth sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }} />
              </Grid>
            </Grid>
          </form>
        </Box>

        <Box sx={{ flexShrink: 0, borderTop: 1, borderColor: 'divider', p: 2 }}>
          <Grid container justifyContent="flex-end" spacing={1}>
            <Grid item>
              <Button variant="outlined" onClick={onClose} size="small">
                Cancel
              </Button>
            </Grid>
            <Grid item>
              <Button variant="contained" color="primary" onClick={handleSubmit(onSubmit)} size="small">
                Save
              </Button>
            </Grid>
          </Grid>
        </Box>
      </Box>
    </Drawer>
  );
};

export default AllocateCreditsDrawer; 