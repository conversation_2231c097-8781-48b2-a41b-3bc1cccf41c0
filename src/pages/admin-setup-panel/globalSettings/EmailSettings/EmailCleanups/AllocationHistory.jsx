import React, { useState } from 'react';
import { Box, Button, Grid } from '@mui/material';
import CustomDataGrid from 'components/custom-components/CustomDataGrid';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';

const AllocationHistory = ({ onBack }) => {
  const [paginationModel, setPaginationModel] = useState({ pageSize: 10, page: 0 });

  const columns = [
    { field: 'userName', headerName: 'USER NAME', flex: 1 },
    { field: 'groupName', headerName: 'GROUP NAME', flex: 1 },
    { field: 'date', headerName: 'DATE', flex: 1 },
    { field: 'creditsConsumed', headerName: 'CREDITS CONSUMED', flex: 1, type: 'number' },
    { field: 'validList', headerName: 'VALID LIST', flex: 1 },
    { field: 'invalidList', headerName: 'INVALID LIST', flex: 1 },
    { field: 'actualList', headerName: 'ACTUAL LIST', flex: 1 }
  ];

  const rows = [];

  return (
    <Box>
      <Grid container justifyContent="flex-end" sx={{ mb: 2 }}>
        <Button variant="outlined" size="small" startIcon={<ArrowBackIcon />} onClick={onBack}>
          Back
        </Button>
      </Grid>
      <CustomDataGrid
        rows={rows}
        columns={columns}
        paginationModel={paginationModel}
        onPaginationModelChange={setPaginationModel}
        rowCount={rows.length}
        sx={{
          '& .MuiDataGrid-columnHeaders': {
            backgroundColor: '#f5f5f5',
            fontWeight: 'bold'
          }
        }}
      />
    </Box>
  );
};

export default AllocationHistory; 