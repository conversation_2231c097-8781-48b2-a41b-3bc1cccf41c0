import React, { useState } from 'react';
import { Grid, Switch, FormControlLabel, Button } from '@mui/material';
import CustomCardHeader from 'components/custom-components/CustomCardHeader';
import Divider from '@mui/material/Divider';
import ReactDraft from './ReactDraft';
import CustomInputLabel from 'components/custom-components/CustomInputLabel';

function Disclaimer() {
  const [checked, setChecked] = useState(true);

  const handleChange = (event) => {
    setChecked(event.target.checked);
  };

  return (
    <>
      <CustomCardHeader
        secondary={
          <Button variant="contained" size="small" color="primary">
            Save
          </Button>
        }
      />
      <Divider sx={{ mb: 1 }} />
      <Grid container spacing={2} alignItems="center">
        <Grid item xs={12}>
          <Grid container spacing={2} alignItems="center" justifyContent="space-between">
            <Grid item xs={12} sm={8} lg={6} xl={6}>
              <CustomInputLabel sx={{ whiteSpace: 'normal', textOverflow: 'unset', overflow: 'visible' }}>
                Would you like to attach a disclaimer to your mass emails?
              </CustomInputLabel>
            </Grid>
            <Grid item xs={12} sm={4} lg={6} xl={6} style={{ textAlign: 'left' }}>
              <FormControlLabel control={<Switch checked={checked} onChange={handleChange} color="primary" />} label="" />
            </Grid>
          </Grid>

          {checked && (
            <Grid item xs={12} sx={{ mt: 2 }}>
              <ReactDraft />
            </Grid>
          )}
        </Grid>
      </Grid>
    </>
  );
}

export default Disclaimer;
