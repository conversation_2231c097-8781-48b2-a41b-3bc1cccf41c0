import React from 'react';
import { Drawer, Box, Typography, IconButton, Grid, Button } from '@mui/material';
import { Close } from '@mui/icons-material';
import { useForm } from 'react-hook-form';
import CustomInputLabel from 'components/custom-components/CustomInputLabel';
import CustomNameField from 'components/custom-components/CustomNameField';
import CustomDropdownField from 'components/custom-components/CustomDropdownField';

const defaultFormValues = {
  user: '',
  individual: '',
  teams: '',
  type: '',
  credits: '',
  renewalType: 'None'
};

const userOptions = [
  { value: 'Individaul', label: 'Individual' },
  { value: 'Teams', label: 'Teams' }
];

const individualOptions = [
  { value: 'Nagaraju', label: 'Nagaraju' },
  { value: 'Prudhvi Kanmuri', label: '<PERSON><PERSON>h<PERSON> Kanmuri' },
  { value: 'Radhika G', label: 'Radhika G' }
];

const teamsOptions = [
  { value: 'Dev Team', label: 'Dev Team' },
  { value: 'QA Team', label: 'QA Team' }
];

const typeOptions = [
  { value: 'Manual', label: 'Manual' },
  { value: 'Automatic', label: 'Automatic' }
];

const renewalTypeOptions = [
  { value: 'None', label: 'None' },
  { value: 'Monthly', label: 'Monthly' },
  { value: 'Yearly', label: 'Yearly' }
];

const MassMailAllocateCreditsDrawer = ({ open, onClose, onAdd }) => {
  const { control, handleSubmit, reset, watch } = useForm({
    defaultValues: defaultFormValues
  });

  const userType = watch('user');

  React.useEffect(() => {
    if (open) {
      reset(defaultFormValues);
    }
  }, [open, reset]);

  const onSubmit = (data) => {
    const finalData = { ...data };
    if (data.user === 'Individaul') {
      finalData.user = data.individual;
    } else if (data.user === 'Teams') {
      finalData.user = data.teams;
    }
    onAdd(finalData);
    onClose();
  };

  return (
    <Drawer anchor="right" open={open} onClose={onClose}>
      <Box
        sx={{
          width: { xs: '90vw', sm: 500 },
          display: 'flex',
          flexDirection: 'column',
          height: '100%'
        }}
        role="presentation"
      >
        <Box sx={{ flexShrink: 0, borderBottom: 1, borderColor: 'divider', p: 2 }}>
          <Grid container justifyContent="space-between" alignItems="center">
            <Grid item>
              <Typography variant="h5">Allocate Credits</Typography>
            </Grid>
            <Grid item>
              <IconButton onClick={onClose} size="small">
                <Close />
              </IconButton>
            </Grid>
          </Grid>
        </Box>

        <Box sx={{ flexGrow: 1, overflowY: 'auto', p: 3 }}>
          <form onSubmit={handleSubmit(onSubmit)}>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <CustomInputLabel required>Allocate To</CustomInputLabel>
                <CustomDropdownField
                  name="user"
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                  control={control}
                  options={userOptions}
                  placeholder="Select User"
                  fullWidth
                />
              </Grid>

              {userType === 'Individaul' && (
                <Grid item xs={12}>
                  <CustomInputLabel required>Individual</CustomInputLabel>
                  <CustomDropdownField
                    sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                    name="individual"
                    control={control}
                    options={individualOptions}
                    placeholder="Select Individual"
                    fullWidth
                  />
                </Grid>
              )}

              {userType === 'Teams' && (
                <Grid item xs={12}>
                  <CustomInputLabel required>Team</CustomInputLabel>
                  <CustomDropdownField
                    sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                    name="teams"
                    control={control}
                    options={teamsOptions}
                    placeholder="Select Team"
                    fullWidth
                  />
                </Grid>
              )}

              <Grid item xs={12}>
                <CustomInputLabel required>Type</CustomInputLabel>
                <CustomDropdownField
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                  name="type"
                  control={control}
                  options={typeOptions}
                  placeholder="Select a Type"
                  fullWidth
                />
              </Grid>
              <Grid item xs={12}>
                <CustomInputLabel required>Credits</CustomInputLabel>
                <CustomNameField sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }} control={control} name="credits" fullWidth />
              </Grid>
              <Grid item xs={12}>
                <CustomInputLabel>Renewal Type</CustomInputLabel>
                <CustomDropdownField
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                  name="renewalType"
                  control={control}
                  options={renewalTypeOptions}
                  fullWidth
                />
              </Grid>
            </Grid>
          </form>
        </Box>

        <Box sx={{ flexShrink: 0, borderTop: 1, borderColor: 'divider', p: 2 }}>
          <Grid container justifyContent="flex-end" spacing={1}>
            <Grid item>
              <Button variant="outlined" onClick={onClose} size="small">
                Cancel
              </Button>
            </Grid>
            <Grid item>
              <Button variant="contained" color="primary" onClick={handleSubmit(onSubmit)} size="small">
                Save
              </Button>
            </Grid>
          </Grid>
        </Box>
      </Box>
    </Drawer>
  );
};

export default MassMailAllocateCreditsDrawer;
