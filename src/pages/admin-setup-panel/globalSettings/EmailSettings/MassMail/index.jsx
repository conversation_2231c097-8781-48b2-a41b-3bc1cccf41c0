import React, { useState } from 'react';
import { Box, Grid, <PERSON>ton, Link, Typography, Paper, Divider, IconButton, Tooltip, Menu, MenuItem } from '@mui/material';
import { useForm } from 'react-hook-form';
import CustomNameField from 'components/custom-components/CustomNameField';
import CustomDataGrid from 'components/custom-components/CustomDataGrid';
import AllocateCreditsDrawer from './AllocateCreditsDrawer';
import EditAllocationDrawer from './EditAllocationDrawer';
import AllocationHistory from '../EmailCleanups/AllocationHistory';
import MoreVertIcon from '@mui/icons-material/MoreVert';

function MassMail() {
  const { control } = useForm({
    defaultValues: {
      search: ''
    }
  });

  const [rows, setRows] = useState([]);
  const [paginationModel, setPaginationModel] = useState({ pageSize: 10, page: 0 });
  const [allocateDrawerOpen, setAllocateDrawerOpen] = useState(false);
  const [editDrawerOpen, setEditDrawerOpen] = useState(false);
  const [selectedAllocation, setSelectedAllocation] = useState(null);
  const [showHistory, setShowHistory] = useState(false);

  const handleAllocateDrawerOpen = () => {
    setAllocateDrawerOpen(true);
  };

  const handleAllocateDrawerClose = () => {
    setAllocateDrawerOpen(false);
  };

  const handleEditDrawerOpen = (row) => {
    setSelectedAllocation(row);
    setEditDrawerOpen(true);
  };

  const handleEditDrawerClose = () => {
    setEditDrawerOpen(false);
    setSelectedAllocation(null);
  };

  const handleAddCredit = (data) => {
    const newAllocation = {
      id: rows.length + 1,
      name: data.user,
      type: data.type,
      allocatedCredits: data.credits,
      renewalCredits: 0, // Placeholder
      availableCredits: data.credits,
      usedCredits: 0,
      allocatedBy: 'Admin' // Placeholder
    };
    setRows([...rows, newAllocation]);
  };

  const handleUpdateCredit = (updatedData) => {
    setRows(
      rows.map((row) => (row.id === updatedData.id ? { ...row, allocatedCredits: updatedData.credits, type: updatedData.type } : row))
    );
  };

  const ActionCell = ({ row }) => {
    const [anchorEl, setAnchorEl] = useState(null);
    const open = Boolean(anchorEl);

    const handleMenuClick = (event) => {
      setAnchorEl(event.currentTarget);
    };

    const handleMenuClose = () => {
      setAnchorEl(null);
    };

    const onEdit = () => {
      handleEditDrawerOpen(row);
      handleMenuClose();
    };

    return (
      <>
        <Tooltip title="Actions">
          <IconButton onClick={handleMenuClick} size="small">
            <MoreVertIcon />
          </IconButton>
        </Tooltip>
        <Menu anchorEl={anchorEl} open={open} onClose={handleMenuClose}>
          <MenuItem onClick={onEdit}>Edit</MenuItem>
        </Menu>
      </>
    );
  };

  const columns = [
    { field: 'name', headerName: 'NAME', flex: 1 },
    { field: 'type', headerName: 'TYPE', flex: 1 },
    { field: 'allocatedCredits', headerName: 'ALLOCATED CREDITS', flex: 1, type: 'number' },
    { field: 'renewalCredits', headerName: 'RENEWAL CREDITS', flex: 1, type: 'number' },
    { field: 'availableCredits', headerName: 'AVAILABLE CREDITS', flex: 1, type: 'number' },
    { field: 'usedCredits', headerName: 'USED CREDITS', flex: 1, type: 'number' },
    { field: 'allocatedBy', headerName: 'ALLOCATED BY', flex: 1 },
    {
      field: 'actions',
      headerName: 'ACTIONS',
      flex: 1,
      sortable: false,
      renderCell: (params) => <ActionCell row={params.row} />
    }
  ];

  if (showHistory) {
    return <AllocationHistory onBack={() => setShowHistory(false)} />;
  }

  return (
    <Box sx={{pt:2}}>
      <Grid container spacing={2} alignItems="center" sx={{ mb: 2 }}>
        <Grid item xs={12} sm={6} md={3}>
          <CustomNameField
            name="search"
            sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
            control={control}
            placeholder="Search"
            fullWidth
          />
        </Grid>
        <Grid
          item
          xs={12}
          sm={6}
          md={9}
          sx={{
            display: 'flex',
            flexDirection: { xs: 'column', sm: 'row' },
            justifyContent: { sm: 'flex-end' },
            alignItems: { xs: 'flex-start', sm: 'center' },
            gap: 2
          }}
        >
          <Link href="#" size="small" underline="hover" sx={{ color: '#1890ff', cursor: 'pointer' }}>
            Delete All
          </Link>
          <Link
            size="small"
            onClick={() => setShowHistory(true)}
            component="button"
            underline="hover"
            sx={{ color: '#1890ff', cursor: 'pointer' }}
          >
            Allocation History
          </Link>
          <Button size="small" variant="contained" color="primary" sx={{ textTransform: 'none' }} onClick={handleAllocateDrawerOpen}>
            + Allocate Credits
          </Button>
        </Grid>
      </Grid>

      <Paper variant="outlined" sx={{ p: 2, mb: 2, backgroundColor: '#f5f5f5' }}>
        <Grid container spacing={2} alignItems="center" textAlign="center">
          <Grid item xs>
            <Typography variant="body2" color="textSecondary">
              Available Credits
            </Typography>
            <Typography variant="h6" sx={{ color: 'green' }}>
              100
            </Typography>
          </Grid>
          <Divider orientation="vertical" flexItem />
          <Grid item xs>
            <Typography variant="body2" color="textSecondary">
              Allocated Credits
            </Typography>
            <Typography variant="h6" sx={{ color: 'blue' }}>
              0
            </Typography>
          </Grid>
          <Divider orientation="vertical" flexItem />
          <Grid item xs>
            <Typography variant="body2" color="textSecondary">
              Allocated Credits Used
            </Typography>
            <Typography variant="h6" sx={{ color: 'orange' }}>
              0
            </Typography>
          </Grid>
          <Divider orientation="vertical" flexItem />
          <Grid item xs>
            <Typography variant="body2" color="textSecondary">
              Credits Balance
            </Typography>
            <Typography variant="h6" sx={{ color: 'red' }}>
              0
            </Typography>
          </Grid>
        </Grid>
      </Paper>

      <CustomDataGrid
        rows={rows}
        columns={columns}
        paginationModel={paginationModel}
        onPaginationModelChange={setPaginationModel}
        rowCount={rows.length}
        sx={{
          '& .MuiDataGrid-columnHeaders': {
            backgroundColor: '#f5f5f5',
            fontWeight: 'bold'
          }
        }}
      />
      <AllocateCreditsDrawer open={allocateDrawerOpen} onClose={handleAllocateDrawerClose} onAdd={handleAddCredit} />
      <EditAllocationDrawer
        open={editDrawerOpen}
        onClose={handleEditDrawerClose}
        onUpdate={handleUpdateCredit}
        allocationData={selectedAllocation}
      />
    </Box>
  );
}

export default MassMail;
