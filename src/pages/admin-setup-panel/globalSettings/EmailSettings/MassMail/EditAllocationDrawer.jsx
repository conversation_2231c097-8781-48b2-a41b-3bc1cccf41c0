import React, { useEffect } from 'react';
import { Drawer, Box, Typography, IconButton, Grid, Button } from '@mui/material';
import { Close } from '@mui/icons-material';
import { useForm } from 'react-hook-form';
import CustomInputLabel from 'components/custom-components/CustomInputLabel';
import CustomN<PERSON><PERSON>ield from 'components/custom-components/CustomNameField';
import CustomDropdownField from 'components/custom-components/CustomDropdownField';

const typeOptions = [
  { value: 'Manual', label: 'Manual' },
  { value: 'Automatic', label: 'Automatic' }
];

const renewalTypeOptions = [
  { value: 'None', label: 'None' },
  { value: 'Monthly', label: 'Monthly' },
  { value: 'Yearly', label: 'Yearly' }
];

const EditAllocationDrawer = ({ open, onClose, onUpdate, allocationData }) => {
  const { control, handleSubmit, reset } = useForm();

  useEffect(() => {
    if (open && allocationData) {
      reset({
        user: allocationData.name,
        type: allocationData.type,
        credits: allocationData.allocatedCredits,
        renewalType: 'None' // Assuming this is the default or needs to be derived
      });
    }
  }, [open, allocationData, reset]);

  const onSubmit = (data) => {
    onUpdate({ ...allocationData, ...data });
    onClose();
  };

  return (
    <Drawer anchor="right" open={open} onClose={onClose}>
      <Box
        sx={{
          width: { xs: '90vw', sm: 500 },
          display: 'flex',
          flexDirection: 'column',
          height: '100%'
        }}
        role="presentation"
      >
        <Box sx={{ flexShrink: 0, borderBottom: 1, borderColor: 'divider', p: 2 }}>
          <Grid container justifyContent="space-between" alignItems="center">
            <Grid item>
              <Typography variant="h5">Edit Credits</Typography>
            </Grid>
            <Grid item>
              <IconButton onClick={onClose} size="small">
                <Close />
              </IconButton>
            </Grid>
          </Grid>
        </Box>

        <Box sx={{ flexGrow: 1, overflowY: 'auto', p: 3 }}>
          <form onSubmit={handleSubmit(onSubmit)}>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <CustomInputLabel required>User</CustomInputLabel>
                <CustomNameField control={control} name="user" fullWidth disabled sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }} />
              </Grid>
              <Grid item xs={12}>
                <CustomInputLabel required>Type</CustomInputLabel>
                <CustomDropdownField
                  name="type"
                  control={control}
                  options={typeOptions}
                  fullWidth
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              </Grid>
              <Grid item xs={12}>
                <CustomInputLabel required>Credits</CustomInputLabel>
                <CustomNameField control={control} name="credits" fullWidth sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }} />
              </Grid>
              <Grid item xs={12}>
                <CustomInputLabel>Renewal Type</CustomInputLabel>
                <CustomDropdownField
                  name="renewalType"
                  control={control}
                  options={renewalTypeOptions}
                  fullWidth
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              </Grid>
            </Grid>
          </form>
        </Box>

        <Box sx={{ flexShrink: 0, borderTop: 1, borderColor: 'divider', p: 2 }}>
          <Grid container justifyContent="flex-end" spacing={1}>
            <Grid item>
              <Button variant="outlined" onClick={onClose} size="small">
                Cancel
              </Button>
            </Grid>
            <Grid item>
              <Button variant="contained" color="primary" onClick={handleSubmit(onSubmit)} size="small">
                Save
              </Button>
            </Grid>
          </Grid>
        </Box>
      </Box>
    </Drawer>
  );
};

export default EditAllocationDrawer; 