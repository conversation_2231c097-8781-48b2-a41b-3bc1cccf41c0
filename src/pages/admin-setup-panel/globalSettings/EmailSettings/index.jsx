import React, { useState } from 'react';
import { Box, Tabs, Tab, Card } from '@mui/material';
import EmailCleanups from './EmailCleanups';
import Disclaimer from './Disclaimer';
import MassMail from './MassMail';
import MiscSettings from './MiscSettings';
import MainCard from 'components/MainCard';
import { borderRadius } from '@mui/system';

function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div role="tabpanel" hidden={value !== index} id={`simple-tabpanel-${index}`} aria-labelledby={`simple-tab-${index}`} {...other}>
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

function a11yProps(index) {
  return {
    id: `simple-tab-${index}`,
    'aria-controls': `simple-tabpanel-${index}`
  };
}

function EmailSettings() {
  const [value, setValue] = useState(0);

  const handleChange = (event, newValue) => {
    setValue(newValue);
  };

  return (
    <MainCard sx={{borderRadius:'0'}}>
      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs value={value} onChange={handleChange} aria-label="email settings tabs" variant="scrollable"
        scrollButtons="auto" sx={{
          mt:-2,
          ml: -2,
          alignItems: 'flex-start',
          justifyContent: 'flex-start',
          borderBottom: 0.2,
          borderColor: 'divider'
        }}>
          <Tab label="Email Cleanups" {...a11yProps(0)} />
          <Tab label="Disclaimer" {...a11yProps(1)} />
          <Tab label="Mass Mail" {...a11yProps(2)} />
          <Tab label="Misc Settings" {...a11yProps(3)} />
        </Tabs>
      </Box>
      <Box>
        {value === 0 && <EmailCleanups />}
        {value === 1 && <Disclaimer />}
        {value === 2 && <MassMail />}
        {value === 3 && <MiscSettings />}
      </Box>
    </MainCard>
  );
}

export default EmailSettings;
