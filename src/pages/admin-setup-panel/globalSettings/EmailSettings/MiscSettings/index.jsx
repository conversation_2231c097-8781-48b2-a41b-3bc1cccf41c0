import React from 'react';
import { Grid, FormHelperText, Button } from '@mui/material';
import CustomCardHeader from 'components/custom-components/CustomCardHeader';
import Divider from '@mui/material/Divider';
import CustomDropdownField from 'components/custom-components/CustomDropdownField';
import { useForm } from 'react-hook-form';
import CustomInputLabel from 'components/custom-components/CustomInputLabel';
import CustomNameField from 'components/custom-components/CustomNameField';

function MiscSettings() {
  const { control } = useForm();

  return (
    <>
      <CustomCardHeader
        secondary={
          <Button variant="contained" size="small" color="primary">
            Save
          </Button>
        }
      />
      <Divider sx={{ mb: 1 }} />
      <Grid container spacing={3}>
        {/* Job Share Default Fields */}
        <Grid item xs={12} sm={6}>
          <CustomInputLabel>Job Share Default Fields</CustomInputLabel>
          <FormHelperText>(Choose the default fields to be selected when the job is being shared in the mass email)</FormHelperText>
        </Grid>
        <Grid item xs={12} sm={6}>
          <CustomDropdownField
            control={control}
            name="job_share_fields"
            placeholder="Select a Field"
            options={[]}
            fullWidth
            sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
          />
        </Grid>

        {/* Hotlist Default Fields */}
        <Grid item xs={12} sm={6}>
          <CustomInputLabel>Hotlist Default Fields</CustomInputLabel>
          <FormHelperText>(Choose the default fields to be selected when the hotlist is being shared in the mass email)</FormHelperText>
        </Grid>
        <Grid item xs={12} sm={6}>
          <CustomDropdownField
            control={control}
            name="hotlist_fields"
            placeholder="Select a Field"
            options={[]}
            fullWidth
            sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
          />
        </Grid>

        {/* Applicable Roles */}
        <Grid item xs={12} sm={6}>
          <CustomInputLabel>Applicable Roles</CustomInputLabel>
          <FormHelperText>(Choose the roles that can override these default columns on their View Profile page)</FormHelperText>
        </Grid>
        <Grid item xs={12} sm={6}>
          <CustomDropdownField
            control={control}
            name="applicable_roles"
            placeholder="Select a Role"
            options={[]}
            fullWidth
            sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
          />
        </Grid>

        {/* Default Email Merge Count */}
        <Grid item xs={12} sm={6}>
          <CustomInputLabel>Default Email Merge Count</CustomInputLabel>
          <FormHelperText>
            (Enter the maximum number of emails to be sent while doing mail merge from the advanced search. Maximum number allowed is 999)
          </FormHelperText>
        </Grid>
        <Grid item xs={12} sm={6}>
          <CustomNameField control={control} name="email_merge_count" fullWidth sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }} />
        </Grid>

        {/* Send mail merge summary notification */}
        <Grid item xs={12} sm={6}>
          <CustomInputLabel>Send mail merge summary notification</CustomInputLabel>
          <FormHelperText>
            (The system will send mail merge summary email notification to configured recipients when a mail merge is sent)
          </FormHelperText>
        </Grid>
        <Grid item xs={12} sm={6}>
          <CustomDropdownField
            control={control}
            name="summary_notification"
            placeholder="Select"
            options={[]}
            fullWidth
            sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
          />
        </Grid>

        {/* Send merge notification */}
        <Grid item xs={12} sm={6}>
          <CustomInputLabel>Send merge notification</CustomInputLabel>
          <FormHelperText>
            (The system will send merge summary email notification to configured recipients when any record is merged)
          </FormHelperText>
        </Grid>
        <Grid item xs={12} sm={6}>
          <CustomDropdownField
            control={control}
            name="merge_notification"
            placeholder="Select"
            options={[
              { value: 'Prudhvi Kanmuri (User)', label: 'Prudhvi Kanmuri (User)' },
              { value: 'Radhika G (User)', label: 'Radhika G (User)' },
              { value: 'Nagaraju (User)', label: 'Nagaraju (User)' }
            ]}
            fullWidth
            sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
          />
        </Grid>

        {/* Limit mail merge notifications */}
        <Grid item xs={12} sm={6}>
          <CustomInputLabel>Limit mail merge notifications to the candidate</CustomInputLabel>
          <FormHelperText>
            (The System will allow only configured number of mail merge notifications to the candidate per day irrespective of the jobs)
          </FormHelperText>
        </Grid>
        <Grid item xs={12} sm={6}>
          <CustomNameField control={control} name="limit_notifications" fullWidth sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }} />
        </Grid>

        {/* Mail Sender */}
        <Grid item xs={12} sm={6}>
          <CustomInputLabel>Mail Sender</CustomInputLabel>
          <FormHelperText>
            (The system will send mail merge summary email notification to configured recipients when a mail merge is sent)
          </FormHelperText>
        </Grid>
        <Grid item xs={12} sm={6}>
          <CustomDropdownField
            control={control}
            name="mail_sender"
            placeholder="Select"
            options={[
              { value: 'Prudhvi Kanmuri (User)', label: 'Prudhvi Kanmuri (User)' },
              { value: 'Radhika G (User)', label: 'Radhika G (User)' },
              { value: 'Nagaraju (User)', label: 'Nagaraju (User)' }
            ]}
            fullWidth
            sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
          />
        </Grid>
      </Grid>
    </>
  );
}

export default MiscSettings;
