import React, { useEffect } from 'react';
import {
  Drawer,
  Box,
  Typography,
  IconButton,
  Grid,
  Chip,
  RadioGroup,
  FormControlLabel,
  Radio,
  Checkbox,
  Switch,
  Button,
  FormGroup
} from '@mui/material';
import { Close } from '@mui/icons-material';
import { useForm, Controller } from 'react-hook-form';
import CustomInputLabel from 'components/custom-components/CustomInputLabel';
import CustomNameField from 'components/custom-components/CustomNameField';
import CustomDropdownField from 'components/custom-components/CustomDropdownField';

const dataTypes = [
  'Text',
  'Multi-line Text',
  'Date',
  'Checkbox',
  'Multiple Choice',
  'Dropdown',
  'Multiple Dropdown',
  'Radio Button',
  'User',
  'Attachment'
];

const defaultFormValues = {
  fieldLabel: '',
  fieldVisibility: 'org_level',
  talentBench: false,
  placement: false,
  isRequired: false,
  fieldDescription: '',
  fieldDataType: 'Text',
  validationRule: 'free_text',
  minLength: '',
  maxLength: '',
  isActive: true,
  options: [{ value: '' }],
  uploadLimit: 1,
  selectedDocumentTypes: []
};

const AddCustomFieldApplicantDrawer = ({ open, onClose }) => {
  const { control, watch, setValue, handleSubmit, reset } = useForm({
    defaultValues: defaultFormValues
  });

  useEffect(() => {
    if (open) {
      reset(defaultFormValues);
    }
  }, [open, reset]);

  const fieldVisibility = watch('fieldVisibility');
  const fieldDataType = watch('fieldDataType');
  const options = watch('options');

  const handleDataTypeToggle = (type) => setValue('fieldDataType', type);
  const addOption = () => setValue('options', [...options, { value: '' }]);
  const removeOption = (index) =>
    setValue(
      'options',
      options.filter((_, i) => i !== index)
    );

  const onSubmit = (data) => {
    console.log(data);
    onClose();
  };

  const renderConditionalFields = () => {
    switch (fieldDataType) {
      case 'Text':
        return (
          <>
            <Grid item xs={12}>
              <CustomInputLabel>Validation Rules</CustomInputLabel>
              <CustomDropdownField
                name="validationRule"
                control={control}
                options={[
                  { value: 'free_text', label: 'Free Text' },
                  { value: 'email', label: 'Email' },
                  { value: 'number', label: 'Number' },
                  { value: 'url', label: 'Url' }
                ]}
                fullWidth
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <CustomInputLabel>Field Min Length</CustomInputLabel>
              <CustomNameField name="minLength" control={control} fullWidth sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }} />
            </Grid>
            <Grid item xs={12} sm={6}>
              <CustomInputLabel>Field Max Length</CustomInputLabel>
              <CustomNameField name="maxLength" control={control} fullWidth sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }} />
            </Grid>
          </>
        );
      case 'Attachment':
        return (
          <>
            <Grid item xs={12}>
              <CustomInputLabel>Maximum Uploaded Limit(MB&apos;s)</CustomInputLabel>
              <CustomDropdownField
                name="uploadLimit"
                control={control}
                options={[{ value: 1, label: '1' }]}
                fullWidth
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Grid>
            <Grid item xs={12}>
              <CustomInputLabel>Select Document Types</CustomInputLabel>
              <CustomDropdownField
                name="selectedDocumentTypes"
                control={control}
                options={[
                  'jpg',
                  'png',
                  'JPG',
                  'PNG',
                  'doc',
                  'ppt',
                  'pptx',
                  'docx',
                  'otf',
                  'pdf',
                  'txt',
                  'DOC',
                  'PDF',
                  'XLS',
                  'xls',
                  'XLSX',
                  'xlsx',
                  'jpeg',
                  'JPEG',
                  'EML',
                  'eml',
                  'msg'
                ].map((type) => ({ label: type, value: type }))}
                fullWidth
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Grid>
          </>
        );
      case 'Dropdown':
      case 'Multiple Choice':
      case 'Multiple Dropdown':
      case 'Radio Button':
        return (
          <Grid item xs={12}>
            <CustomInputLabel>Options</CustomInputLabel>
            {options.map((option, index) => (
              <Grid container spacing={1} key={index} alignItems="center" sx={{ mb: 1 }}>
                <Grid item xs={10}>
                  <CustomNameField
                    name={`options[${index}].value`}
                    control={control}
                    placeholder={`Option ${index + 1}`}
                    fullWidth
                    sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                  />
                </Grid>
                <Grid item xs={2}>
                  <IconButton onClick={() => removeOption(index)} size="small">
                    <span style={{ fontSize: 18 }}>🗑️</span>
                  </IconButton>
                </Grid>
              </Grid>
            ))}
            <Button onClick={addOption} size="small">
              Add Option
            </Button>
          </Grid>
        );
      default:
        return null;
    }
  };

  return (
    <Drawer anchor="right" open={open} onClose={onClose}>
      <Box sx={{ width: { xs: '90vw', sm: 500 }, display: 'flex', flexDirection: 'column', height: '100vh' }} role="presentation">
        <Box sx={{ flexShrink: 0, borderBottom: 1, borderColor: 'divider', p: 2 }}>
          <Grid container justifyContent="space-between" alignItems="center">
            <Grid item>
              <Typography variant="h5">Add Custom Field</Typography>
            </Grid>
            <Grid item>
              <IconButton onClick={onClose} size="small">
                <Close />
              </IconButton>
            </Grid>
          </Grid>
        </Box>
        <Box sx={{ flexGrow: 1, overflowY: 'auto', p: 3 }}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <CustomInputLabel required>Field Label</CustomInputLabel>
              <CustomNameField control={control} name="fieldLabel" fullWidth sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }} />
            </Grid>
            <Grid item xs={12}>
              <CustomInputLabel>Field visibility</CustomInputLabel>
              <Controller
                name="fieldVisibility"
                control={control}
                render={({ field }) => (
                  <RadioGroup {...field} row>
                    <FormControlLabel value="org_level" control={<Radio />} label="Org level" />
                    <FormControlLabel value="business_unit" control={<Radio />} label="Business unit" />
                  </RadioGroup>
                )}
              />
            </Grid>
            {fieldVisibility === 'org_level' && (
              <Grid item xs={12}>
                <FormGroup>
                  <FormControlLabel
                    control={<Controller name="talentBench" control={control} render={({ field }) => <Checkbox {...field} />} />}
                    label="Also Add Field to the Talent Bench"
                  />
                  <FormControlLabel
                    control={<Controller name="placement" control={control} render={({ field }) => <Checkbox {...field} />} />}
                    label="Also Add Field to the Placement"
                  />
                  <FormControlLabel
                    control={<Controller name="isRequired" control={control} render={({ field }) => <Checkbox {...field} />} />}
                    label="Is Required"
                  />
                </FormGroup>
              </Grid>
            )}
            {fieldVisibility === 'business_unit' && (
              <Grid item xs={12}>
                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <CustomInputLabel>Business Unit</CustomInputLabel>
                    <CustomDropdownField
                      name="businessUnit"
                      control={control}
                      options={[{ value: 'unit1', label: 'Unit 1' }]}
                      fullWidth
                      sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                    />
                  </Grid>
                </Grid>
              </Grid>
            )}
            <Grid item xs={12}>
              <CustomInputLabel>Field Description</CustomInputLabel>
              <CustomNameField
                control={control}
                name="fieldDescription"
                multiline
                rows={4}
                fullWidth
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Grid>
            <Grid item xs={12}>
              <CustomInputLabel>Field Data Type</CustomInputLabel>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 1 }}>
                {dataTypes.map((type) => (
                  <Chip
                    key={type}
                    label={type}
                    onClick={() => handleDataTypeToggle(type)}
                    color={fieldDataType === type ? 'primary' : 'default'}
                  />
                ))}
              </Box>
            </Grid>
            {renderConditionalFields()}
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Controller name="isActive" control={control} render={({ field }) => <Switch {...field} checked={field.value} />} />
                }
                label="Is Active"
              />
            </Grid>
          </Grid>
        </Box>
        <Box sx={{ flexShrink: 0, borderTop: 1, borderColor: 'divider', p: 2 }}>
          <Grid container justifyContent="flex-end" spacing={1}>
            <Grid item>
              <Button variant="outlined" onClick={onClose} size="small">
                Cancel
              </Button>
            </Grid>
            <Grid item>
              <Button variant="contained" color="primary" onClick={handleSubmit(onSubmit)} size="small">
                Save
              </Button>
            </Grid>
          </Grid>
        </Box>
      </Box>
    </Drawer>
  );
};

export default AddCustomFieldApplicantDrawer;
