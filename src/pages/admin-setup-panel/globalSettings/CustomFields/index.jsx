import React, { useState } from 'react';
import { <PERSON>po<PERSON>, Box, Button, Grid, Tooltip, IconButton, Switch, Menu, MenuItem, Divider } from '@mui/material';
import { Clock } from 'iconsax-react';
import MainCard from 'components/MainCard';
import CustomDataGrid from 'components/custom-components/CustomDataGrid';
import CustomNameField from 'components/custom-components/CustomNameField';
import CustomDropdownField from 'components/custom-components/CustomDropdownField';
import CustomInputLabel from 'components/custom-components/CustomInputLabel';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import { useForm } from 'react-hook-form';
import EditCustomFieldDrawer from './All/EditCustomFieldDrawer';
import AddCustomFieldDrawer from './All/AddCustomFieldDrawer';
import AddCustomFieldJobPostingDrawer from './JobPosting/AddCustomFieldJobPostingDrawer';
import EditCustomFieldJobPostingDrawer from './JobPosting/EditCustomFieldJobPostingDrawer';
import AddCustomFieldApplicantDrawer from './Applicant/AddCustomFieldApplicantDrawer';
import EditCustomFieldApplicantDrawer from './Applicant/EditCustomFieldApplicantDrawer';
import AddCustomFieldTalentBenchDrawer from './TalentBench/AddCustomFieldTalentBenchDrawer';
import EditCustomFieldTalentBenchDrawer from './TalentBench/EditCustomFieldTalentBenchDrawer';
import AddCustomFieldClientsDrawer from './Clients/AddCustomFieldClientsDrawer';
import EditCustomFieldClientsDrawer from './Clients/EditCustomFieldClientsDrawer';
import AddCustomFieldVendorsDrawer from './Vendors/AddCustomFieldVendorsDrawer';
import EditCustomFieldVendorsDrawer from './Vendors/EditCustomFieldVendorsDrawer';
import AddCustomFieldClientContactsDrawer from './ClientContacts/AddCustomFieldClientContactsDrawer';
import EditCustomFieldClientContactsDrawer from './ClientContacts/EditCustomFieldClientContactsDrawer';
import AddCustomFieldVendorContactsDrawer from './VendorContacts/AddCustomFieldVendorContactsDrawer';
import EditCustomFieldVendorContactsDrawer from './VendorContacts/EditCustomFieldVendorContactsDrawer';

function CustomFields() {
  // 10 rows per category
  const jobPostingRows = Array.from({ length: 10 }, (_, i) => ({
    id: 100 + i,
    fieldLabel: `Job Posting Field ${i + 1}`,
    businessUnit: 'BusinessUnitJP',
    syncWith: 'N/A',
    moduleName: 'Job Posting',
    isIndependent: true,
    customFieldType: 'General Custom Field',
    createdBy: 'User JP',
    modifiedBy: 'User JP',
    createdAt: '2023-12-07 02:05:50',
    lastModified: '2023-12-07 02:05:50',
    status: i % 2 === 0
  }));
  const applicantRows = Array.from({ length: 10 }, (_, i) => ({
    id: 200 + i,
    fieldLabel: `Applicant Field ${i + 1}`,
    businessUnit: 'BusinessUnitA',
    syncWith: 'N/A',
    moduleName: 'Applicant',
    isIndependent: true,
    customFieldType: 'General Custom Field',
    createdBy: 'User A',
    modifiedBy: 'User A',
    createdAt: '2023-12-07 02:05:50',
    lastModified: '2023-12-07 02:05:50',
    status: i % 2 === 0
  }));
  const talentBenchRows = Array.from({ length: 10 }, (_, i) => ({
    id: 300 + i,
    fieldLabel: `Talent Bench Field ${i + 1}`,
    businessUnit: 'BusinessUnitTB',
    syncWith: 'N/A',
    moduleName: 'Talent Bench',
    isIndependent: true,
    customFieldType: 'General Custom Field',
    createdBy: 'User TB',
    modifiedBy: 'User TB',
    createdAt: '2023-12-07 02:05:50',
    lastModified: '2023-12-07 02:05:50',
    status: i % 2 === 0
  }));
  const clientsRows = Array.from({ length: 10 }, (_, i) => ({
    id: 400 + i,
    fieldLabel: `Clients Field ${i + 1}`,
    businessUnit: 'BusinessUnitC',
    syncWith: 'N/A',
    moduleName: 'Clients',
    isIndependent: true,
    customFieldType: 'General Custom Field',
    createdBy: 'User C',
    modifiedBy: 'User C',
    createdAt: '2023-12-07 02:05:50',
    lastModified: '2023-12-07 02:05:50',
    status: i % 2 === 0
  }));
  const vendorsRows = Array.from({ length: 10 }, (_, i) => ({
    id: 500 + i,
    fieldLabel: `Vendors Field ${i + 1}`,
    businessUnit: 'BusinessUnitV',
    syncWith: 'N/A',
    moduleName: 'Vendors',
    isIndependent: true,
    customFieldType: 'General Custom Field',
    createdBy: 'User V',
    modifiedBy: 'User V',
    createdAt: '2023-12-07 02:05:50',
    lastModified: '2023-12-07 02:05:50',
    status: i % 2 === 0
  }));
  const clientContactsRows = Array.from({ length: 10 }, (_, i) => ({
    id: 600 + i,
    fieldLabel: `Client Contacts Field ${i + 1}`,
    businessUnit: 'BusinessUnitCC',
    syncWith: 'N/A',
    moduleName: 'Client Contacts',
    isIndependent: true,
    customFieldType: 'General Custom Field',
    createdBy: 'User CC',
    modifiedBy: 'User CC',
    createdAt: '2023-12-07 02:05:50',
    lastModified: '2023-12-07 02:05:50',
    status: i % 2 === 0
  }));
  const vendorContactsRows = Array.from({ length: 10 }, (_, i) => ({
    id: 700 + i,
    fieldLabel: `Vendor Contacts Field ${i + 1}`,
    businessUnit: 'BusinessUnitVC',
    syncWith: 'N/A',
    moduleName: 'Vendor Contacts',
    isIndependent: true,
    customFieldType: 'General Custom Field',
    createdBy: 'User VC',
    modifiedBy: 'User VC',
    createdAt: '2023-12-07 02:05:50',
    lastModified: '2023-12-07 02:05:50',
    status: i % 2 === 0
  }));

  // Combine all for 'All' category
  const allRows = [
    ...jobPostingRows,
    ...applicantRows,
    ...talentBenchRows,
    ...clientsRows,
    ...vendorsRows,
    ...clientContactsRows,
    ...vendorContactsRows
  ];

  const { control, watch } = useForm({
    defaultValues: {
      category: 'All',
      search: ''
    }
  });
  const selectedCategory = watch('category');

  // Filter rows based on selected category
  let filteredRows = allRows;
  switch (selectedCategory) {
    case 'Job Posting':
      filteredRows = jobPostingRows;
      break;
    case 'Applicant':
      filteredRows = applicantRows;
      break;
    case 'Talent Bench':
      filteredRows = talentBenchRows;
      break;
    case 'Clients':
      filteredRows = clientsRows;
      break;
    case 'Vendors':
      filteredRows = vendorsRows;
      break;
    case 'Client Contacts':
      filteredRows = clientContactsRows;
      break;
    case 'Vendor Contacts':
      filteredRows = vendorContactsRows;
      break;
    default:
      filteredRows = allRows;
  }

  const [paginationModel, setPaginationModel] = useState({ pageSize: 10, page: 0 });
  const [editDrawerOpen, setEditDrawerOpen] = useState(false);
  const [addDrawerOpen, setAddDrawerOpen] = useState(false);
  const [selectedField, setSelectedField] = useState(null);

  const categoryOptions = [
    { value: 'All', label: 'all' },
    { value: 'Job Posting', label: 'job_posting' },
    { value: 'Applicant', label: 'applicant' },
    { value: 'Talent Bench', label: 'talent_bench' },
    { value: 'Clients', label: 'clients' },
    { value: 'Vendors', label: 'vendors' },
    { value: 'Client Contacts', label: 'client_contacts' },
    { value: 'Vendor Contacts', label: 'vendor_contacts' }
  ];

  const handleEditClick = (field) => {
    setSelectedField(field);
    setEditDrawerOpen(true);
  };

  const handleEditDrawerClose = () => {
    setEditDrawerOpen(false);
    setSelectedField(null);
  };

  const handleAddDrawerOpen = () => {
    setAddDrawerOpen(true);
  };

  const handleAddDrawerClose = () => {
    setAddDrawerOpen(false);
  };

  const ActionCell = ({ row }) => {
    const [anchorEl, setAnchorEl] = useState(null);
    const open = Boolean(anchorEl);

    const handleMenuClick = (event) => {
      setAnchorEl(event.currentTarget);
    };

    const handleMenuClose = () => {
      setAnchorEl(null);
    };

    const onEdit = () => {
      handleEditClick(row);
      handleMenuClose();
    };

    return (
      <Box sx={{ display: 'flex', alignItems: 'center' }}>
        <Tooltip title="Actions">
          <IconButton size="small" onClick={handleMenuClick}>
            <MoreVertIcon sx={{ fontSize: '1rem' }} />
          </IconButton>
        </Tooltip>
        <Menu anchorEl={anchorEl} open={open} onClose={handleMenuClose}>
          <MenuItem onClick={onEdit}>Edit</MenuItem>
        </Menu>
        <Switch checked={row.status} onChange={() => handleStatusChange(row.id)} size="small" />
      </Box>
    );
  };

  const columns = [
    {
      field: 'fieldLabel',
      headerName: 'FIELD LABEL',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => <Typography variant="body2">{params.value}</Typography>
    },
    {
      field: 'businessUnit',
      headerName: 'BUSINESS UNIT',
      flex: 1,
      minWidth: 120,
      renderCell: (params) => <Typography variant="body2">{params.value}</Typography>
    },
    {
      field: 'syncWith',
      headerName: 'SYNC WITH',
      flex: 1,
      minWidth: 120,
      renderCell: (params) => <Typography variant="body2">{params.value}</Typography>
    },
    {
      field: 'moduleName',
      headerName: 'MODULE NAME',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => <Typography variant="body2">{params.value}</Typography>
    },
    {
      field: 'isIndependent',
      headerName: 'IS INDEPENDENT FIELD',
      flex: 1,
      minWidth: 180,
      renderCell: (params) => <Typography variant="body2">{params.value ? 'Yes' : 'No'}</Typography>
    },
    {
      field: 'customFieldType',
      headerName: 'CUSTOM FIELD TYPE',
      flex: 1,
      minWidth: 180,
      renderCell: (params) => <Typography variant="body2">{params.value}</Typography>
    },
    {
      field: 'createdBy',
      headerName: 'CREATED BY',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => <Typography variant="body2">{params.value}</Typography>
    },
    {
      field: 'modifiedBy',
      headerName: 'MODIFIED BY',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => <Typography variant="body2">{params.value}</Typography>
    },
    {
      field: 'createdAt',
      headerName: 'CREATED AT',
      flex: 1,
      minWidth: 180,
      renderCell: (params) => <Typography variant="body2">{params.value}</Typography>
    },
    {
      field: 'lastModified',
      headerName: 'LAST MODIFIED',
      flex: 1,
      minWidth: 180,
      renderCell: (params) => <Typography variant="body2">{params.value}</Typography>
    },
    {
      field: 'actions',
      headerName: 'ACTIONS',
      flex: 0.5,
      minWidth: 120,
      sortable: false,
      renderCell: (params) => <ActionCell row={params.row} />
    }
  ];

  // Drawer rendering logic using switch-case style
  const renderDrawers = () => {
    switch (selectedCategory) {
      case 'Job Posting':
        return (
          <>
            <EditCustomFieldJobPostingDrawer open={editDrawerOpen} onClose={handleEditDrawerClose} fieldData={selectedField} />
            <AddCustomFieldJobPostingDrawer open={addDrawerOpen} onClose={handleAddDrawerClose} />
          </>
        );
      case 'Applicant':
        return (
          <>
            <EditCustomFieldApplicantDrawer open={editDrawerOpen} onClose={handleEditDrawerClose} fieldData={selectedField} />
            <AddCustomFieldApplicantDrawer open={addDrawerOpen} onClose={handleAddDrawerClose} />
          </>
        );
      case 'Talent Bench':
        return (
          <>
            <EditCustomFieldTalentBenchDrawer open={editDrawerOpen} onClose={handleEditDrawerClose} fieldData={selectedField} />
            <AddCustomFieldTalentBenchDrawer open={addDrawerOpen} onClose={handleAddDrawerClose} />
          </>
        );
      case 'Clients':
        return (
          <>
            <EditCustomFieldClientsDrawer open={editDrawerOpen} onClose={handleEditDrawerClose} fieldData={selectedField} />
            <AddCustomFieldClientsDrawer open={addDrawerOpen} onClose={handleAddDrawerClose} />
          </>
        );
      case 'Vendors':
        return (
          <>
            <EditCustomFieldVendorsDrawer open={editDrawerOpen} onClose={handleEditDrawerClose} fieldData={selectedField} />
            <AddCustomFieldVendorsDrawer open={addDrawerOpen} onClose={handleAddDrawerClose} />
          </>
        );
      case 'Client Contacts':
        return (
          <>
            <EditCustomFieldClientContactsDrawer open={editDrawerOpen} onClose={handleEditDrawerClose} fieldData={selectedField} />
            <AddCustomFieldClientContactsDrawer open={addDrawerOpen} onClose={handleAddDrawerClose} />
          </>
        );
      case 'Vendor Contacts':
        return (
          <>
            <EditCustomFieldVendorContactsDrawer open={editDrawerOpen} onClose={handleEditDrawerClose} fieldData={selectedField} />
            <AddCustomFieldVendorContactsDrawer open={addDrawerOpen} onClose={handleAddDrawerClose} />
          </>
        );
      default:
        return (
          <>
            <EditCustomFieldDrawer open={editDrawerOpen} onClose={handleEditDrawerClose} fieldData={selectedField} />
            <AddCustomFieldDrawer open={addDrawerOpen} onClose={handleAddDrawerClose} />
          </>
        );
    }
  };

  return (
    <MainCard
      title="Custom Fields"
      sx={{ borderRadius: '1%', backgroundColor: 'white', '& .MuiInputLabel-root': { fontSize: '0.875rem' } }}
    >
      <Grid container spacing={2} sx={{ mb: 2 }}>
        <Grid item xs={12} md={8}>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} sm={2} md={1.5}>
              <CustomInputLabel>Categories</CustomInputLabel>
            </Grid>
            <Grid item xs={12} sm={4} md={3}>
              <CustomDropdownField
                name="category"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                control={control}
                options={categoryOptions}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={4}>
              <CustomNameField control={control} sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }} name="search" placeholder="Search" />
            </Grid>
          </Grid>
        </Grid> 
        <Grid item xs={12} md={4}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, justifyContent: { xs: 'flex-start', md: 'flex-end' } }}>
            <Button variant="outlined" size="small" startIcon={<Clock size="18" color="gray" />}>
              Activities
            </Button>
            <Button variant="contained" size="small" color="primary" onClick={handleAddDrawerOpen}>
              + Add
            </Button>
          </Box>
        </Grid>
      </Grid>
      <Divider sx={{ mb: 1 }} />

      <Grid container spacing={2}>
        <Grid item xs={12}>
          <Box sx={{ width: '100%' }}>
            <CustomDataGrid
              rows={filteredRows}
              columns={columns}
              paginationModel={paginationModel}
              onPaginationModelChange={setPaginationModel}
              rowCount={filteredRows.length}
              sx={{
                '& .MuiDataGrid-columnHeaders': {
                  fontWeight: 'bold'
                },
                overflowX: 'auto'
              }}
            />
          </Box>
        </Grid>
      </Grid>
      {renderDrawers()}
    </MainCard>
  );
}

export default CustomFields;
