import { InputLabel } from "@mui/material";

const CustomInputLabel = ({ htmlFor, children, sx = {} }) => {
  return (
    <InputLabel
      htmlFor={htmlFor}
      sx={{
        // fontFamily: "Roboto",
        fontSize: "13px",
        // fontWeight: 400,
        // color: "black",
        whiteSpace: 'normal',
        display: 'block',
        wordBreak: 'break-word',
        ...sx
      }}
    >
      {children}
    </InputLabel>
  );
};

export default CustomInputLabel;