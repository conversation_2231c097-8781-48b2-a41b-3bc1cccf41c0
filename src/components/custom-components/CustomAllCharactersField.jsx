import React from 'react';
import { TextField, FormControl } from '@mui/material';
import { Controller } from 'react-hook-form';

const CustomAllCharactersField = ({
  name,
  control,
  placeholder,
  defaultValue = "",
  disabled = false,
  required,
  sx = {},
  ...props
}) => {
  const sanitizedPlaceholder = placeholder?.replace(/\bEnter\b/g, '').trim();

  const isRequired =
    typeof required !== 'undefined'
      ? required
      : sanitizedPlaceholder
      ? `${sanitizedPlaceholder} is required`
      : false;

  return (
    <FormControl fullWidth>
      <Controller
        name={name}
        control={control}
        defaultValue={defaultValue.trim()}
        rules={{
          required: isRequired,
          validate: (value) => {
            const trimmed = value.trim();
            if (!trimmed) return 'This field cannot be empty or just spaces';
            return true;
          },
        }}
        render={({ field, fieldState }) => (
          <TextField
            {...field}
            placeholder={placeholder}
            size="small" 
            disabled={disabled}
            sx={{
              // borderRadius: "2px",
              // "& fieldset": {
              //   borderRadius: "2px",
              // },
              '& .MuiInputBase-input::placeholder': {
                fontStyle: 'Inter var',
                color: 'rgba(0, 0, 0, 0.6)',
              },
              '& .MuiInputBase-input': {
                textTransform: 'none', // Allow user's manual capitalization
              },
              "& .MuiFormHelperText-root": {
                backgroundColor: "white !important",
                padding: "2px 4px",
                margin: 0,
              },
              '& .MuiOutlinedInput-root': {
                '&:hover .MuiOutlinedInput-notchedOutline': {
                  borderColor: 'primary.main'
                },
                '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                  borderColor: 'primary.main'
                }
              },
              ...sx,
            }}
            error={Boolean(fieldState?.error)}
            helperText={fieldState?.error?.message}
            onChange={(e) => {
              let value = e.target.value;

              // Allow all characters, spaces, and punctuation
              value = value
                .replace(/\s{2,}/g, ' ')         // Replace multiple spaces with a single space
                .replace(/^\s+/, '');            // Remove leading spaces

              // Capitalize only the first letter of the entire input string
              value = value.charAt(0).toUpperCase() + value.slice(1);

              field.onChange(value);
            }}
            onBlur={() => {
              let cleaned = field.value.trim();

              if (cleaned.length > 0) {
                cleaned = cleaned
                  .replace(/\s{2,}/g, ' ');  // Collapse multiple spaces

                cleaned = cleaned.charAt(0).toUpperCase() + cleaned.slice(1);  // Capitalize first letter
              }

              field.onChange(cleaned);
            }}
            {...props}
          />
        )}
      />
    </FormControl>
  );
};

export default CustomAllCharactersField;
