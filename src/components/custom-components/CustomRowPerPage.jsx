import React from 'react';
import PropTypes from 'prop-types';
import { Typography, FormControl, Select, MenuItem, Box } from '@mui/material';

const RowsPerPageSelector = ({ getState, setPageSize, options = [10, 15, 20, 25, 50, 100] }) => {
  const rowsPerPage = getState().pagination.pageSize; // Get the current page size

  const handleRowsPerPageChange = (event) => {
    const newRowsPerPage = Number(event.target.value);
    setPageSize(newRowsPerPage); // Update the page size externally
  };

  return (
    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
      {/* Label for Rows Per Page */}
      <Typography variant="caption" color="secondary">
        Rows per page
      </Typography>

      {/* Dropdown to select Rows Per Page */}
      <FormControl size="small">
        <Select
          value={rowsPerPage}
          onChange={handleRowsPerPageChange} // Use the updated handler
          size="small"
          sx={{
            '& .MuiSelect-select': {
              py: 0.75,
              px: 1.25
            },
            '& .MuiSelect-icon': {
              color: 'white' // Change the dropdown arrow color to white
            },
            borderRadius: '6px',
            backgroundColor: 'primary.main',
            borderColor: 'none',
            color: 'white',
          }}
        >
          {/* Generate MenuItems dynamically with custom styling */}
          {options.map((option) => (
            <MenuItem
              key={option}
              value={option}
              sx={{
                backgroundColor: rowsPerPage === option ? 'primary.main' : 'white',
                color: rowsPerPage === option ? 'white' : 'black',
                '&:hover': {
                  backgroundColor: 'primary.main',
                  color: 'white'
                }
              }}
            >
              {option}
            </MenuItem>
          ))}
        </Select>
      </FormControl>
    </Box>
  );
};

RowsPerPageSelector.propTypes = {
  getState: PropTypes.func.isRequired, // Function to get the current state
  setPageSize: PropTypes.func.isRequired, // Function to update the page size
  options: PropTypes.arrayOf(PropTypes.number) // Options for rows per page (optional)
};

export default RowsPerPageSelector;
