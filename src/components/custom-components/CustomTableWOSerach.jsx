import React from 'react';
import PropTypes from 'prop-types';
import { Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Divider } from '@mui/material';
import { flexRender } from '@tanstack/react-table';
import { RowSelection } from 'components/third-party/react-table';
import ScrollX from 'components/ScrollX';

const CustomGrid = ({ table, rowSelection }) => {
  return (
    <ScrollX>
      {/* Table selection info */}
      <RowSelection selected={Object.keys(rowSelection).length} />

      {/* Table container */}
      <TableContainer component={Paper} sx={{ maxHeight: '200px', overflowY: 'auto' }}>
        <Table>
          {/* Table header */}
          <TableHead>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableCell
                    key={header.id}
                    {...header.column.columnDef.meta}
                    sx={{ padding: '3px', fontSize: '0.75rem', color: 'primary.main' }}
                  >
                    {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableHead>

          {/* Table body */}
          <TableBody>
            {(() => {
              const rows = table?.getRowModel()?.rows || [];
              const totalRows = 1; // Always ensure 10 rows
              const emptyCells = table.getAllColumns().length;

              if (rows?.length === 0) {
                return (
                  <TableRow>
                    <TableCell
                      colSpan={emptyCells}
                      align="center"
                      sx={{ padding: '20px', fontSize: '1rem', color: 'gray', height: '150px' }}
                    >
                      No Data Found
                    </TableCell>
                  </TableRow>
                );
              }

              return (
                <>
                  {rows.map((row) => (
                    <TableRow key={row.id}>
                      {row.getVisibleCells().map((cell) => (
                        <TableCell key={cell.id} {...cell.column.columnDef.meta} sx={{ padding: '3px', fontSize: '0.75rem' }}>
                          {flexRender(cell.column.columnDef.cell, cell.getContext())}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))}

                  {Array.from({ length: Math.max(totalRows - rows.length, 0) }).map((_, index) => (
                    <TableRow key={`empty-${index}`}>
                      {table.getAllColumns().map((column) => (
                        <TableCell key={column.id} sx={{ padding: '3px', fontSize: '0.75rem', color: 'transparent' }}>
                          &nbsp;
                        </TableCell>
                      ))}
                    </TableRow>
                  ))}
                </>
              );
            })()}
          </TableBody>
        </Table>
      </TableContainer>

      <Divider />
    </ScrollX>
  );
};

CustomGrid.propTypes = {
  table: PropTypes.object.isRequired,
  rowSelection: PropTypes.object.isRequired
};

export default CustomGrid;
