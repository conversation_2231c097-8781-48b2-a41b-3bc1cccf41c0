import React from 'react';
import { Controller } from 'react-hook-form';
import { TextField, FormControl } from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker'; // From @mui/x-date-pickers

const CustomDobPicker = ({
  name,
  control,
  label = "Date of Birth",
  required = true,
  disabled = false,
  minDate = new Date('1900-01-01'),
  maxDate = new Date(), // by default till today
  sx = {},
  ...props
}) => {
  const isRequired = required ? `${label} is required` : false;

  return (
    <FormControl fullWidth>
      <Controller
        name={name}
        control={control}
        defaultValue={null}
        rules={{
          required: isRequired,
          validate: (value) => {
            if (!value) return `${label} is required`;
            if (value > new Date()) return "Date of birth cannot be in the future";
            return true;
          }
        }}
        render={({ field, fieldState }) => (
          <DatePicker
            {...field}
            disabled={disabled}
            label={label}
            inputFormat="dd/MM/yyyy"
            disableFuture
            minDate={minDate}
            maxDate={maxDate}
            onChange={(date) => {
              field.onChange(date);
            }}
            renderInput={(params) => (
              <TextField
                {...params}
                size="small"
                sx={{
                  "& fieldset": {
                    borderRadius: "2px",
                  },
                  "& .MuiFormHelperText-root": {
                    backgroundColor: "white",
                    margin: 0,
                    padding: "2px 4px",
                  },
                  '& .MuiOutlinedInput-root': {
                    '&:hover .MuiOutlinedInput-notchedOutline': {
                      borderColor: 'primary.main'
                    },
                    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                      borderColor: 'primary.main'
                    }
                  },
                  ...sx,
                }}
                error={Boolean(fieldState.error)}
                helperText={fieldState.error?.message}
              />
            )}
            {...props}
          />
        )}
      />
    </FormControl>
  );
};

export default CustomDobPicker;
