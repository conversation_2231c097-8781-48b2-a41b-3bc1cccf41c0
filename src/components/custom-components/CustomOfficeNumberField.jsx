import React from 'react';
import { TextField, FormControl } from '@mui/material';
import { Controller } from 'react-hook-form';

const CustomOfficeNumberField = ({ name, control, placeholder, defaultValue = '', sx = {}, ...props }) => {
  const sanitizedPlaceholder = placeholder?.replace(/\bEnter\b/g, '').trim();
  const isRequired = sanitizedPlaceholder ? `${sanitizedPlaceholder} is required` : false;

  // Valid STD codes — extend this list if needed
  const validStdCodes = ['011', '022', '033', '044', '040', '080'];

  return (
    <FormControl fullWidth>
      <Controller
        name={name}
        control={control}
        defaultValue={defaultValue}
        rules={{
          pattern: {
            value: /^(011|022|033|044|040|080)-\d{8}$/,
            message: 'Office Number must be in format STD-CODE-XXXXXXXX (e.g., 040-12345678)'
          }
        }}
        render={({ field, fieldState }) => (
          <TextField
            {...field}
            placeholder={placeholder}
            size="small"
            sx={{
              // borderRadius: "2px",
              // "& fieldset": {
              //   borderRadius: "2px",
              // },
              '& .MuiInputBase-input::placeholder': {
                fontStyle: 'Inter var',
                color: 'rgba(0, 0, 0, 0.6)'
              },
              '& .MuiFormHelperText-root': {
                backgroundColor: 'white !important',
                padding: '2px 4px',
                margin: 0
              },
              '& .MuiOutlinedInput-root': {
                '&:hover .MuiOutlinedInput-notchedOutline': {
                  borderColor: 'primary.main'
                },
                '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                  borderColor: 'primary.main'
                }
              },
              ...sx
            }}
            error={Boolean(fieldState?.error)}
            helperText={fieldState?.error?.message}
            inputProps={{
              maxLength: 12, // STD (3) + dash (1) + 8 digits = 12
              inputMode: 'numeric'
            }}
            onChange={(e) => {
              let rawValue = e.target.value.replace(/[^0-9]/g, ''); // Remove non-numeric
              let formattedValue = '';

              if (rawValue.length <= 3) {
                formattedValue = rawValue;
              } else {
                formattedValue = rawValue.slice(0, 3) + '-' + rawValue.slice(3, 11); // STD + 8-digit
              }

              field.onChange(formattedValue);
            }}
            {...props}
          />
        )}
      />
    </FormControl>
  );
};

export default CustomOfficeNumberField;
