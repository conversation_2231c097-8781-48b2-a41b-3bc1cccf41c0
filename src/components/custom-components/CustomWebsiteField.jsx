import React from 'react';
import { TextField, FormControl } from '@mui/material';
import { Controller } from 'react-hook-form';

const CustomWebsiteField = ({ name, control, placeholder, sx = {}, ...props }) => {
  return (
    <FormControl fullWidth>
      <Controller
        name={name}
        control={control}
        rules={{
          required: 'Website is required', // Required validation
          pattern: {
            value: /^(https?:\/\/)?([a-z0-9-]+\.)+[a-z0-9]{2,7}(\/[a-z0-9-]*)*\/?$/, // Basic URL validation (http://, https://, or without)
            message: 'Please enter a valid website URL'
          }
        }}
        render={({ field, fieldState }) => (
          <TextField
            {...field} // Spread the field props from react-hook-form
            placeholder={placeholder} // Placeholder for the input
            size="small"
            sx={{
              borderRadius: '2px',
              '& .MuiSelect-select': {},
              // "& fieldset": {
              //   borderRadius: "2px",
              // },
              '& .MuiInputBase-input::placeholder': {
                // Targets placeholder text
                fontStyle: 'Intra van', // Makes it italic
                color: 'rgba(0, 0, 0, 0.6)' // Adjusts placeholder color for better visibility
              },
              '& .MuiFormHelperText-root': {
                backgroundColor: 'white !important', // ✅ force solid background
                padding: '2px 4px',
                margin: 0
              },
              '& .MuiOutlinedInput-root': {
                '&:hover .MuiOutlinedInput-notchedOutline': {
                  borderColor: 'primary.main'
                },
                '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                  borderColor: 'primary.main'
                }
              },
              ...sx
            }}
            InputLabelProps={{ shrink: true }} // Ensure label is always shrunk
            error={Boolean(fieldState?.error)} // Show error if validation fails
            helperText={fieldState?.error?.message} // Show error message from fieldState
            onChange={(e) => {
              // Remove leading spaces from the input
              const value = e.target.value.replace(/^\s+/, '');
              field.onChange(value); // Update the field value without leading spaces
            }}
            {...props} // Spread any other props (e.g., placeholder, etc.)
          />
        )}
      />
    </FormControl>
  );
};

export default CustomWebsiteField;
