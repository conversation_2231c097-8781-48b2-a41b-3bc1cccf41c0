import React, { useState } from 'react';
import { Autocomplete, TextField, Grid, FormHelperText } from '@mui/material';

// Mock Data
const countryStateCityData = {
  USA: {
    California: ['Los Angeles', 'San Francisco', 'San Diego'],
    Texas: ['Houston', 'Austin', 'Dallas'],
  },
  India: {
    Maharashtra: ['Mumbai', 'Pune', 'Nagpur'],
    Karnataka: ['Bangalore', 'Mysore', 'Mangalore'],
  },
  Canada: {
    Ontario: ['Toronto', 'Ottawa', 'Hamilton'],
    Alberta: ['Calgary', 'Edmonton', 'Red Deer'],
  },
};

const CascadingDropdown = () => {
  const [countries] = useState(Object.keys(countryStateCityData)); // List of countries
  const [states, setStates] = useState([]); // List of states for the selected country
  const [cities, setCities] = useState([]); // List of cities for the selected state

  const [selectedCountry, setSelectedCountry] = useState(null);
  const [selectedState, setSelectedState] = useState(null);
  const [selectedCity, setSelectedCity] = useState(null);

  // Error states
  const [countryError, setCountryError] = useState(false);
  const [stateError, setStateError] = useState(false);
  const [cityError, setCityError] = useState(false);

  const handleCountryChange = (event, value) => {
    setSelectedCountry(value);
    setStates(value ? Object.keys(countryStateCityData[value]) : []);
    setCities([]); // Clear cities when country changes
    setSelectedState(null);
    setSelectedCity(null);
    setCountryError(!value); // Set error if no country selected
  };

  const handleStateChange = (event, value) => {
    setSelectedState(value);
    const allCities = value ? countryStateCityData[selectedCountry][value] : [];
    setCities(allCities);
    setSelectedCity(null); // Clear cities when state changes
    setStateError(!value); // Set error if no state selected
  };

  const handleCityChange = (event, value) => {
    setSelectedCity(value);
    setCityError(!value); // Set error if no city selected
  };

  return (
    <Grid container spacing={2}>
      {/* Country Dropdown */}
      <Grid item xs={12} sm={4}>
        <Autocomplete
          size="small"
          fullWidth
          options={countries}
          value={selectedCountry}
          onChange={handleCountryChange}
          renderInput={(params) => (
            <TextField
              {...params}
              placeholder="Select Country"
              error={countryError}
              helperText={countryError ? 'Country is required' : ''}
              variant="outlined"
              sx={{
                backgroundColor: 'white',
                borderRadius: '2px',
                '& fieldset': { borderRadius: '2px' },
              }}
            />
          )}
        />
      </Grid>

      {/* State Dropdown */}
      <Grid item xs={12} sm={4}>
        <Autocomplete
          size="small"
          fullWidth
          options={states}
          value={selectedState}
          onChange={handleStateChange}
          disabled={!selectedCountry}
          renderInput={(params) => (
            <TextField
              {...params}
              placeholder="Select State"
              error={stateError}
              helperText={stateError ? 'State is required' : ''}
              variant="outlined"
              sx={{
                backgroundColor: 'white',
                borderRadius: '2px',
                '& fieldset': { borderRadius: '2px' },
              }}
            />
          )}
        />
      </Grid>

      {/* City Dropdown */}
      <Grid item xs={12} sm={4}>
        <Autocomplete
          size="small"
          fullWidth
          options={cities}
          value={selectedCity}
          onChange={handleCityChange}
          disabled={!selectedState}
          renderInput={(params) => (
            <TextField
              {...params}
              placeholder="Select City"
              error={cityError}
              helperText={cityError ? 'City is required' : ''}
              variant="outlined"
              sx={{
                backgroundColor: 'white',
                borderRadius: '2px',
                '& fieldset': { borderRadius: '2px' },
              }}
            />
          )}
        />
      </Grid>
    </Grid>
  );
};

export default CascadingDropdown;
