import React, { useState } from 'react';
import {
  TextField,
  FormControl,
  Stack,
  Select,
  MenuItem,
  FormHelperText,
  Box
} from '@mui/material';
import { Controller } from 'react-hook-form';
import { NumericFormat } from 'react-number-format';

const CustomCurrencyInputField = ({ name, control, placeholder = 'Amount', defaultValue = '', sx = {}, ...props }) => {
  const currencies = [
    { code: 'INR', symbol: '₹' },
    { code: 'USD', symbol: '$' },
    { code: 'EUR', symbol: '€' },
    { code: 'GBP', symbol: '£' },
    { code: 'JPY', symbol: '¥' },
    // Add more currencies as needed
  ];

  const [selectedCurrencySymbol, setSelectedCurrencySymbol] = useState('₹'); // Default to INR symbol

  return (
    <FormControl fullWidth error={Boolean(control._formState.errors[name])} sx={sx}>
      <Controller
        name={name}
        control={control}
        defaultValue={defaultValue}
        render={({ field, fieldState }) => {
          return (
            <Stack sx={{ gap: 1 }}>
              <Stack direction="row" sx={{ gap: 1, justifyContent: 'space-between', alignItems: 'center' }}>
                <Select
                  value={selectedCurrencySymbol}
                  onChange={(event) => {
                    setSelectedCurrencySymbol(event.target.value);
                  }}
                  sx={{
                    '& .MuiInputBase-input::placeholder': {
                      fontStyle: 'Inter var',
                      color: 'rgba(0, 0, 0, 0.6)'
                    },
                    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                      borderColor: 'primary.main'
                    },
                    '&:hover .MuiOutlinedInput-notchedOutline': {
                      borderColor: 'primary.main'
                    },
                    width: '60px',
                    backgroundColor: 'rgba(248, 249, 250, 1)'
                  }}
                  size="small"
                >
                  {currencies.map((currency) => (
                    <MenuItem key={currency.code} value={currency.symbol}>
                      {currency.symbol}
                    </MenuItem>
                  ))}
                </Select>

                <NumericFormat
                  value={field.value}
                  customInput={TextField}
                  placeholder={placeholder}
                  fullWidth
                  onValueChange={(values) => {
                    field.onChange(values.floatValue);
                  }}
                  onBlur={field.onBlur}
                  decimalScale={2}
                  fixedDecimalScale
                  thousandSeparator={true}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      '&:hover fieldset': { borderColor: 'primary.main' },
                      '&.Mui-focused fieldset': { borderColor: 'primary.main',  }
                    },
                    '& .MuiInputBase-input::placeholder': {
                      fontStyle: 'Inter var',
                      color: 'rgba(0, 0, 0, 0.6)'
                    },
                    backgroundColor: 'rgba(248, 249, 250, 1)'
                  }}
                  size="small"
                  inputMode='numeric'
                />
              </Stack>
            </Stack>
          );
        }}
      />
      {control._formState.errors[name] && <FormHelperText>{control._formState.errors[name].message}</FormHelperText>}
    </FormControl>
  );
};

export default CustomCurrencyInputField; 