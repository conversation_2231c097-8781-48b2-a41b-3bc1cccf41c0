import React from 'react';
import { TextField, FormControl } from '@mui/material';
import { Controller } from 'react-hook-form';

const CustomPanCardInput = ({
  name,
  control,
  placeholder = 'Enter PAN Card Number',
  defaultValue = "",
  disabled = false,
  required = true,
  sx = {},
  ...props
}) => {
  const isRequired = required ? 'PAN Card number is required' : false;

  // Strict PAN validation: 5 letters, 4 numbers, 1 letter
  const isValidPanCard = (value) => /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/.test(value);

  // Format PAN Card while typing
  const formatPanCard = (value) => {
    value = value.replace(/[^A-Za-z0-9]/g, '').toUpperCase(); // remove non-letters/digits and make uppercase

    let formatted = '';

    // Build character by character
    for (let i = 0; i < value.length && i < 10; i++) {
      if (i < 5) {
        // First 5 must be letters
        if (/[A-Z]/.test(value[i])) {
          formatted += value[i];
        }
      } else if (i < 9) {
        // Next 4 must be digits
        if (/[0-9]/.test(value[i])) {
          formatted += value[i];
        }
      } else if (i === 9) {
        // Last one must be letter
        if (/[A-Z]/.test(value[i])) {
          formatted += value[i];
        }
      }
    }

    return formatted;
  };

  return (
    <FormControl fullWidth>
      <Controller
        name={name}
        control={control}
        defaultValue={defaultValue}
        rules={{
          required: isRequired,
          validate: (value) => {
            const trimmed = value.trim();
            if (!trimmed) return 'This field cannot be empty or just spaces';
            if (!isValidPanCard(trimmed)) {
              return 'Please enter a valid PAN Card number in the format **********';
            }
            return true;
          },
        }}
        render={({ field, fieldState }) => (
          <TextField
            {...field}
            placeholder={placeholder}
            size="small"
            disabled={disabled}
            sx={{
              // borderRadius: "2px",
              // "& fieldset": { borderRadius: "2px" },
              '& .MuiInputBase-input::placeholder': {
                fontStyle: 'Intra van',
                color: 'rgba(0, 0, 0, 0.6)',
              },
              "& .MuiFormHelperText-root": {
                backgroundColor: "white !important",
                padding: "2px 4px",
                margin: 0,
              },
              '& .MuiOutlinedInput-root': {
                '&:hover .MuiOutlinedInput-notchedOutline': {
                  borderColor: 'primary.main'
                },
                '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                  borderColor: 'primary.main'
                }
              },
              ...sx,
            }}
            error={Boolean(fieldState?.error)}
            helperText={fieldState?.error?.message}
            onChange={(e) => {
              let value = e.target.value;
              value = formatPanCard(value); // format while typing
              field.onChange(value);
            }}
            onBlur={() => {
              let cleaned = field.value.trim();
              if (!isValidPanCard(cleaned)) {
                field.onChange(''); // clear if invalid on blur
              }
            }}
            {...props}
          />
        )}
      />
    </FormControl>
  );
};

export default CustomPanCardInput;
