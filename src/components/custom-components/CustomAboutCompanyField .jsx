import React from 'react';
import { TextField, FormControl } from '@mui/material';
import { Controller } from 'react-hook-form';

const CustomAboutCompanyField = ({
  name,
  control,
  placeholder,
  defaultValue = "",
  disabled = false,
  required,
  sx = {},
  ...props
}) => {
  return (
    <FormControl fullWidth>
      <Controller
        name={name}
        control={control}
        defaultValue={defaultValue.trim()}
        rules={{
            required: 'About Company is required',
          validate: (value) => {
            const trimmed = value.trim();
            return true; // No required validation
          },
        }}
        render={({ field, fieldState }) => (
          <TextField
            {...field}
            placeholder={placeholder}
            multiline
            minRows={4}
            size="small"
            disabled={disabled}
            sx={{
              // borderRadius: "2px",
              // "& fieldset": {
              //   borderRadius: "2px",
              // },
              '& .MuiInputBase-input::placeholder': {
                fontStyle: 'Inter var',
                color: 'rgba(0, 0, 0, 0.6)',
              },
              '& .MuiInputBase-input': {
                textTransform: 'none',
              },
              "& .MuiFormHelperText-root": {
                backgroundColor: "white !important",
                padding: "2px 4px",
                margin: 0,
              },
              '& .MuiOutlinedInput-root': {
                '&:hover .MuiOutlinedInput-notchedOutline': {
                  borderColor: 'primary.main'
                },
                '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                  borderColor: 'primary.main'
                }
              },
              ...sx,
            }}
            error={Boolean(fieldState?.error)}
            helperText={fieldState?.error?.message}
            onChange={(e) => {
              let value = e.target.value;

              // Clean up spacing
              value = value.replace(/\s{2,}/g, ' ').trimStart();

              // Capitalize first letter if value exists
              if (value.length > 0) {
                value = value.charAt(0).toUpperCase() + value.slice(1);
              }

              field.onChange(value);
            }}
            onBlur={() => {
              let cleaned = field.value.trim().replace(/\s{2,}/g, ' ');

              // Capitalize first letter
              if (cleaned.length > 0) {
                cleaned = cleaned.charAt(0).toUpperCase() + cleaned.slice(1);
              }

              field.onChange(cleaned);
            }}
            {...props}
          />
        )}
      />
    </FormControl>
  );
};

export default CustomAboutCompanyField;
