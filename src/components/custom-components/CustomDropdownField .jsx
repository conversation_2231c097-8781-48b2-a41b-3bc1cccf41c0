import React, { useEffect, useState } from 'react';
import { FormControl, MenuItem, Select } from '@mui/material';
import { Controller } from 'react-hook-form';

const CustomDropdownField = ({
  name,
  control,
  placeholder,
  options = [], // Initial options list
  sx = {},
  defaultValue = '',
  ...props
}) => {
  const [dropdownOptions, setDropdownOptions] = useState(options); // State to track options

  // Update options when new options are passed from the parent
  useEffect(() => {
    setDropdownOptions(options);
  }, [options]);

  return (
    <FormControl fullWidth>
      <Controller
        name={name}
        control={control}
        defaultValue={defaultValue || ''}
        render={({ field }) => (
          <Select
            {...field}
            size="small"
            fullWidth
            value={field.value || ''} // ✅ Ensure field value is used
            onChange={(e) => {
              field.onChange(e.target.value); // ✅ Updates react-hook-form state
              if (props.onChange) {
                props.onChange(e.target.value); // ✅ Calls parent `handleClientChange`
              }
            }}
            displayEmpty
            variant="outlined"
            renderValue={(selected) =>
              selected ? (
                selected
              ) : (
                <em style={{ fontFamily: "'Inter var', sans-serif", fontStyle: 'normal', color: 'rgba(0, 0, 0, 0.6)' }}>{placeholder}</em>
              )
            }
            sx={{
              backgroundColor: sx.backgroundColor || 'white', // ✅ Allow custom background
              // borderRadius: "2px",
              // "& .MuiSelect-select": {
              //   backgroundColor: sx.backgroundColor || "white",
              //   // padding:"8px"
              // },
              // "& fieldset": {
              //   borderRadius: "0px",
              // },
              fontFamily: "'Inter var', sans-serif", // Add fontFamily here
              '& .MuiSelect-select': {
                backgroundColor: sx.backgroundColor || 'white',
                fontFamily: "'Inter var', sans-serif" // And here inside the select area
              },
              '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                borderColor: 'primary.main'
              },
              '&:hover .MuiOutlinedInput-notchedOutline': {
                borderColor: 'primary.main'
              },
              ...sx // ✅ Merge any additional styles
            }}
            MenuProps={{
              PaperProps: {
                sx: {
                  borderRadius: '0px !important',
                  padding: '0 !important',
                  margin: '0 !important'
                }
              },
              MenuListProps: {
                sx: {
                  paddingTop: '0 !important', // Remove padding at the top of the dropdown list
                  paddingBottom: '0 !important' // Remove padding at the bottom of the dropdown list
                }
              }
            }}
            {...props}
          >
            {/* <MenuItem value="" disabled>
              <em>{placeholder}</em>
            </MenuItem> */}
            {dropdownOptions.map((option) => (
              <MenuItem
                key={option.value}
                value={option.value}
                sx={{
                  border: '1px solid transparent',
                  borderRadius: '0px',
                  padding: '4px',
                  // "&:hover": {
                  //   borderColor: "black", // Border color becomes black when hovered
                  //   backgroundColor: "rgba(0, 0, 0, 0.1)", // Light background on hover
                  // },

                  '&.Mui-focusVisible': {
                    borderColor: 'black' // Border color becomes black when clicked/focused
                  }
                }}
              >
                {option.value}
              </MenuItem>
            ))}
          </Select>
        )}
      />
    </FormControl>
  );
};

export default CustomDropdownField;
