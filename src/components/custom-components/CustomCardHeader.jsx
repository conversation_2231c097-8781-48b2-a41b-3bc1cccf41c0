import PropTypes from 'prop-types';
import { forwardRef } from 'react';

// Material-UI
import { useTheme } from '@mui/material/styles';
import Card from '@mui/material/Card';
import CardHeader from '@mui/material/CardHeader';

// Header style
const headerSX = {
  px: 0,
  py :2,
  '& .MuiCardHeader-action': {
    m: '0px auto',
    alignSelf: 'center',
  },

  justifyContent: 'space-between',
};

// ==============================|| CUSTOM CARD COMPONENT ||============================== //

const CustomCardHeader = forwardRef(
  ({ secondary, sx = {}, title }, ref) => {
    const theme = useTheme();

    return (
      <Card
        ref={ref}
        sx={{
          position: 'relative',
          borderRadius: '0px',
          border: 'none',
          backgroundColor: 'transparent',
          boxShadow: 'none',

          ...sx, // Merge additional custom styles
        }}
      >
        <CardHeader
          sx={headerSX}
          title={title}
          titleTypographyProps={{ variant: 'subtitle1' }}
          action={secondary}
        />
      </Card>
    );
  }
);

CustomCardHeader.propTypes = {
  secondary: PropTypes.node, // Content for the action section of the header
  sx: PropTypes.object, // Custom styles for the card
  title: PropTypes.oneOfType([PropTypes.string, PropTypes.node]), // Title for the card
};

export default CustomCardHeader;