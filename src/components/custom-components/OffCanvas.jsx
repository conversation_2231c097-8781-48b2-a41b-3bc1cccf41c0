import React from 'react';
import { Drawer, IconButton, Typography, Box, Stack, Divider, Button } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import PropTypes from 'prop-types';

const OffCanvas = ({ 
  open, 
  onClose, 
  title, 
  children, 
  placement = 'end', 
  size = 'md',
  onSave,
  saveButtonText = 'Save',
  cancelButtonText = 'Cancel'
}) => {
  const getWidth = () => {
    switch (size) {
      case 'sm':
        return '400px';
      case 'md':
        return '500px';
      case 'lg':
        return '800px';
      default:
        return '500px';
    }
  };

  return (
    <Drawer anchor={placement} open={open} onClose={onClose}>
      <Box sx={{ width: { xs: '100%', sm: getWidth() }, height: '100%', display: 'flex', flexDirection: 'column' }}>
        {/* Sticky Header */}
        <Box sx={{ p: 2, position: 'sticky', top: 0, bgcolor: 'background.paper', zIndex: 1 }}>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Typography variant="h5">{title}</Typography>
            <IconButton onClick={onClose}>
              <CloseIcon />
            </IconButton>
          </Box>
        </Box>

        <Divider />

        {/* Content */}
        <Box sx={{ p: 3, flexGrow: 1, overflowY: 'auto' }}>
          {children}
        </Box>

        {/* Footer */}
        <Divider />
        <Box sx={{ p: 2, display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
          <Button
            onClick={onClose}
            size="small"
            variant="outlined"
          >
            {cancelButtonText}
          </Button>
          {onSave && (
            <Button 
              type="submit" 
              size="small" 
              variant="contained" 
              color="primary" 
              onClick={onSave}
            >
              {saveButtonText}
            </Button>
          )}
        </Box>
      </Box>
    </Drawer>
  );
};

OffCanvas.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  title: PropTypes.string.isRequired,
  children: PropTypes.node,
  placement: PropTypes.oneOf(['left', 'right', 'top', 'bottom']),
  size: PropTypes.oneOf(['sm', 'md', 'lg']),
  onSave: PropTypes.func,
  saveButtonText: PropTypes.string,
  cancelButtonText: PropTypes.string
};

export default OffCanvas; 