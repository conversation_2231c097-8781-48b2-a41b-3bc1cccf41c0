import React from 'react';
import { Box } from '@mui/material';
import { DataGrid } from '@mui/x-data-grid';

const CustomDataGrid = ({
  rows,
  columns,
  paginationModel,
  onPaginationModelChange,
  pageSizeOptions = [10, 15, 20, 25, 50, 100],
  rowCount,
  rowHeight = 38,
  headerHeight = 38,
  height = 300,
  width = '100%',
  onRowSelectionModelChange,
  disableRowSelectionOnClick,
  checkboxSelection = false,
}) => {
  return (
    <Box sx={{ height, width }}>
      <DataGrid
        rows={rows}
        columns={columns}
        pagination
        paginationModel={paginationModel}
        onPaginationModelChange={onPaginationModelChange}
        pageSizeOptions={pageSizeOptions}
        rowCount={rowCount}
        paginationMode="server"
        rowHeight={rowHeight}
        headerHeight={headerHeight}
        onRowSelectionModelChange={onRowSelectionModelChange}
        disableRowSelectionOnClick={disableRowSelectionOnClick}
        checkboxSelection={checkboxSelection}
  //        sx={{
  //   '& .MuiDataGrid-columnHeaders': {
  //     backgroundColor: 'primary.main',
  //     fontSize: '13px',
  //     fontWeight: 'bold'
  //   },
  //   '& .MuiDataGrid-cell': {
  //     fontSize: '13px', // Set font size for all cell text
  //   },
  //   '& .MuiDataGrid-row:focus': {
  //     outline: 'primary.main'
  //   },
  //   '& .MuiDataGrid-cell:focus': {
  //     outline: 'none'
  //   },
  //   '& .MuiDataGrid-columnHeaders:focus': {
  //     outline: 'none'
  //   }
  // }}
      />
    </Box>
  );
};

export default CustomDataGrid;
