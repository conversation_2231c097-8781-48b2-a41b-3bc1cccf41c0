import React from 'react';
import Accordion from '@mui/material/Accordion';
import AccordionSummary from '@mui/material/AccordionSummary';
import AccordionDetails from '@mui/material/AccordionDetails';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';

const CustomAccordion = ({
  summary,
  children,
  summarySx = {},
  detailsSx = {},
  accordionSx = {},
  expandIcon = <ExpandMoreIcon sx={{ color: 'primary.main' }} />,
  ...props
}) => (
  <Accordion
    sx={{
      border: 'none',
      boxShadow: 'none',
      mb: 1,
      ...accordionSx
    }}
    {...props}
  >
    <AccordionSummary
      expandIcon={expandIcon}
      sx={{
        bgcolor: '#f5f5f5',
        minHeight: 0,
        '&.Mui-expanded': { minHeight: 0 },
        py: 0.5,
        pl: 0,
        ...summarySx
      }}
    >
      {summary}
    </AccordionSummary>
    <AccordionDetails sx={{ pl: 0, py: 1, ...detailsSx }}>{children}</AccordionDetails>
  </Accordion>
);

export default CustomAccordion; 