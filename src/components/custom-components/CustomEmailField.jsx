import React from 'react';
import { TextField, FormControl } from '@mui/material';
import { Controller } from 'react-hook-form';

const CustomEmailField = ({ name, control, defaultValue = '', placeholder, sx = {}, ...props }) => {
  return (
    <FormControl fullWidth>
      <Controller
        name={name}
        control={control}
        defaultValue={defaultValue}
        rules={{
          required: placeholder ? `${placeholder.replace('Enter', '').trim()} is required` : 'Email is required', // Dynamic required message
          pattern: {
            value: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/, // Email validation regex
            message: 'Please enter a valid email address'
          }
        }}
        render={({ field, fieldState }) => (
          <TextField
            {...field} // Makes it controlled
            value={field.value || ''} // Ensures it updates dynamically
            placeholder={placeholder} // Display placeholder
            size="small"
            sx={{
              // borderRadius: "2px",
              // "& fieldset": {
              //   borderRadius: "2px",
              // },
              '& .MuiInputBase-input::placeholder': {
                fontStyle: 'Inter var',
                color: 'rgba(0, 0, 0, 0.6)'
              },
              '& .MuiFormHelperText-root': {
                backgroundColor: 'white !important', // ✅ force solid background
                padding: '2px 4px',
                margin: 0
              },
              '& .MuiOutlinedInput-root': {
                '&:hover .MuiOutlinedInput-notchedOutline': {
                  borderColor: 'primary.main'
                },
                '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                  borderColor: 'primary.main'
                }
              },
              ...sx
            }}
            error={Boolean(fieldState?.error)} // Shows error state if validation fails
            helperText={fieldState?.error?.message} // Shows error message
            onChange={(e) => {
              field.onChange(e.target.value.trim()); // Trims leading/trailing spaces
            }}
            {...props}
          />
        )}
      />
    </FormControl>
  );
};

export default CustomEmailField;
