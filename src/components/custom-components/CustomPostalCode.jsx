import React from 'react';
import { TextField, FormControl } from '@mui/material';
import { Controller } from 'react-hook-form';

const CustomPostalCodeField = ({ name, control, placeholder, sx = {}, ...props }) => {
  return (
    <FormControl fullWidth>
      <Controller
        name={name}
        control={control}
        rules={{
          required: 'Postal Code is required', // Required validation
          pattern: {
            value: /^(?!0{6})\d{6}$/, // Ensures exactly 6 digits and not all zeros
            message: 'Postal Code must be exactly 6 digits and cannot be all zeros'
          }
        }}
        render={({ field, fieldState }) => (
          <TextField
            {...field} // Spread the field props from react-hook-form
            placeholder={placeholder} // Placeholder
            size="small"
            sx={{
              // borderRadius: "2px",
              // "& fieldset": {
              //   borderRadius: "2px",
              // },
              '& .MuiInputBase-input::placeholder': {
                fontStyle: 'Inter var',
                color: 'rgba(0, 0, 0, 0.6)'
              },
              '& .MuiFormHelperText-root': {
                backgroundColor: 'white !important',
                padding: '2px 4px',
                margin: 0
              },
              '& .MuiOutlinedInput-root': {
                '&:hover .MuiOutlinedInput-notchedOutline': {
                  borderColor: 'primary.main'
                },
                '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                  borderColor: 'primary.main'
                }
              },
              ...sx
            }}
            InputLabelProps={{ shrink: true }} // Ensure label is always shrunk
            error={Boolean(fieldState?.error)} // Show error if validation fails
            helperText={fieldState?.error?.message} // Show error message from fieldState
            inputProps={{
              maxLength: 6, // Max length of 6 characters
              inputMode: 'numeric' // Allow numeric input mode
            }}
            onChange={(e) => {
              // Custom sanitization logic for postal code
              const sanitizedValue = e.target.value
                .replace(/[^0-9]/g, '') // Allow only digits
                .replace(/^\s+/, ''); // Remove leading spaces
              field.onChange(sanitizedValue); // Update field value with sanitized input
            }}
            {...props} // Spread any other props (e.g., placeholder, etc.)
          />
        )}
      />
    </FormControl>
  );
};

export default CustomPostalCodeField;
