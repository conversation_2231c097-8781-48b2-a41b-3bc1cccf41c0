// LoginErrorDialog.jsx
import React from 'react';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';

const LoginErrorDialog = ({ open, onClose }) => {
    console.log("this is error ")
  return (
    <Dialog open={open} maxWidth="xs" fullWidth>
      <DialogContent style={{ textAlign: 'center', padding: '20px' }}>
        <Typography variant="h6" gutterBottom>
          Email or password credentials are invalid.
        </Typography>
        <Button variant="contained" color="primary" onClick={onClose} style={{ marginTop: '20px' }}>
          OKAY
        </Button>
      </DialogContent>
    </Dialog>
  );
};

export default LoginErrorDialog;
