import React from 'react';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import CircularProgress from '@mui/material/CircularProgress';
import Typography from '@mui/material/Typography';

const LoginSuccessDialog = ({ open }) => {
  return (
    <Dialog open={open} maxWidth="xs" fullWidth>
      <DialogContent style={{ textAlign: 'center', padding: '20px' }}>
        <Typography variant="h6" gutterBottom>
          Login Successful. Redirecting to your dashboard.
        </Typography>
        <CircularProgress style={{ marginTop: '20px' }} />
      </DialogContent>
    </Dialog>
  );
};

export default LoginSuccessDialog;
