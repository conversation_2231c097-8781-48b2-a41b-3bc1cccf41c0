// import PropTypes from 'prop-types';
// import { useState } from 'react';
// import { Link as RouterLink } from 'react-router-dom';
// import { preload } from 'swr';

// // material-ui
// import Grid from '@mui/material/Grid';
// import Button from '@mui/material/Button';
// import Checkbox from '@mui/material/Checkbox';
// import Stack from '@mui/material/Stack';
// import Link from '@mui/material/Link';
// import InputLabel from '@mui/material/InputLabel';
// import Typography from '@mui/material/Typography';
// import OutlinedInput from '@mui/material/OutlinedInput';
// import InputAdornment from '@mui/material/InputAdornment';
// import FormHelperText from '@mui/material/FormHelperText';
// import FormControlLabel from '@mui/material/FormControlLabel';

// // third-party
// import * as Yup from 'yup';
// import { Formik } from 'formik';

// // project-imports
// import useAuth from 'hooks/useAuth';
// import useScriptRef from 'hooks/useScriptRef';
// import IconButton from 'components/@extended/IconButton';
// import AnimateButton from 'components/@extended/AnimateButton';
// import { fetcher } from 'utils/axios';

// // assets
// import { Eye, EyeSlash } from 'iconsax-react';

// // ============================|| JWT - LOGIN ||============================ //

// export default function AuthLogin({ forgot }) {
//   const [checked, setChecked] = useState(false);

//   const { isLoggedIn, login } = useAuth();
//   const scriptedRef = useScriptRef();

//   const [showPassword, setShowPassword] = useState(false);
//   const handleClickShowPassword = () => {
//     setShowPassword(!showPassword);
//   };

//   const handleMouseDownPassword = (event) => {
//     event.preventDefault();
//   };

//   return (
//     <>
//       <Formik
//         initialValues={{
//           email: '<EMAIL>',
//           password: '123456',
//           submit: null
//         }}
//         validationSchema={Yup.object().shape({
//           email: Yup.string().email('Must be a valid email').max(255).required('Email is required'),
//           password: Yup.string().max(255).required('Password is required')
//         })}
//         onSubmit={async (values, { setErrors, setStatus, setSubmitting }) => {
//           console.log(values);
//           try {
//             await login(values.email, values.password);
//             if (scriptedRef.current) {
//               setStatus({ success: true });
//               setSubmitting(false);
//               preload('api/menu/dashboard', fetcher); // load menu on login success
//             }
//           } catch (err) {
//             console.error(err);
//             if (scriptedRef.current) {
//               setStatus({ success: false });
//               setErrors({ submit: err.message });
//               setSubmitting(false);
//             }
//           }
//         }}
//       >
//         {({ errors, handleBlur, handleChange, handleSubmit, isSubmitting, touched, values }) => (
//           <form noValidate onSubmit={handleSubmit}>
//             <Grid container spacing={3}>
//               <Grid item xs={12}>
//                 <Stack spacing={1}>
//                   <InputLabel htmlFor="email-login">Email Address</InputLabel>
//                   <OutlinedInput
//                     id="email-login"
//                     type="email"
//                     value={values.email}
//                     name="email"
//                     onBlur={handleBlur}
//                     onChange={handleChange}
//                     placeholder="Enter email address"
//                     fullWidth
//                     error={Boolean(touched.email && errors.email)}
//                   />
//                 </Stack>
//                 {touched.email && errors.email && (
//                   <FormHelperText error id="standard-weight-helper-text-email-login">
//                     {errors.email}
//                   </FormHelperText>
//                 )}
//               </Grid>
//               <Grid item xs={12}>
//                 <Stack spacing={1}>
//                   <InputLabel htmlFor="password-login">Password</InputLabel>
//                   <OutlinedInput
//                     fullWidth
//                     error={Boolean(touched.password && errors.password)}
//                     id="-password-login"
//                     type={showPassword ? 'text' : 'password'}
//                     value={values.password}
//                     name="password"
//                     onBlur={handleBlur}
//                     onChange={handleChange}
//                     endAdornment={
//                       <InputAdornment position="end">
//                         <IconButton
//                           aria-label="toggle password visibility"
//                           onClick={handleClickShowPassword}
//                           onMouseDown={handleMouseDownPassword}
//                           edge="end"
//                           color="secondary"
//                         >
//                           {showPassword ? <Eye /> : <EyeSlash />}
//                         </IconButton>
//                       </InputAdornment>
//                     }
//                     placeholder="Enter password"
//                   />
//                 </Stack>
//                 {touched.password && errors.password && (
//                   <FormHelperText error id="standard-weight-helper-text-password-login">
//                     {errors.password}
//                   </FormHelperText>
//                 )}
//               </Grid>

//               <Grid item xs={12} sx={{ mt: -1 }}>
//                 <Stack direction="row" justifyContent="space-between" alignItems="center" spacing={2}>
//                   <FormControlLabel
//                     control={
//                       <Checkbox
//                         checked={checked}
//                         onChange={(event) => setChecked(event.target.checked)}
//                         name="checked"
//                         color="primary"
//                         size="small"
//                       />
//                     }
//                     label={<Typography variant="h6">Keep me sign in</Typography>}
//                   />

//                   <Link variant="h6" component={RouterLink} to={isLoggedIn && forgot ? forgot : '/forgot-password'} color="text.primary">
//                     Forgot Password?
//                   </Link>
//                 </Stack>
//               </Grid>
//               {errors.submit && (
//                 <Grid item xs={12}>
//                   <FormHelperText error>{errors.submit}</FormHelperText>
//                 </Grid>
//               )}
//               <Grid item xs={12}>
//                 <AnimateButton>
//                   <Button disableElevation disabled={isSubmitting} fullWidth size="large" type="submit" variant="contained" color="primary">
//                     Login
//                   </Button>
//                 </AnimateButton>
//               </Grid>
//             </Grid>
//           </form>
//         )}
//       </Formik>
//     </>
//   );
// }

// AuthLogin.propTypes = { forgot: PropTypes.string };


// import PropTypes from 'prop-types';
// import { useState } from 'react';
// import { Link as RouterLink } from 'react-router-dom';
// import { preload } from 'swr';

// // material-ui
// import Grid from '@mui/material/Grid';
// import Button from '@mui/material/Button';
// import Checkbox from '@mui/material/Checkbox';
// import Stack from '@mui/material/Stack';
// import Link from '@mui/material/Link';
// import InputLabel from '@mui/material/InputLabel';
// import Typography from '@mui/material/Typography';
// import OutlinedInput from '@mui/material/OutlinedInput';
// import InputAdornment from '@mui/material/InputAdornment';
// import FormHelperText from '@mui/material/FormHelperText';
// import FormControlLabel from '@mui/material/FormControlLabel';

// // third-party
// import * as Yup from 'yup';
// import { Formik } from 'formik';

// // project-imports
// import useAuth from 'hooks/useAuth';
// import useScriptRef from 'hooks/useScriptRef';
// import IconButton from 'components/@extended/IconButton';
// import AnimateButton from 'components/@extended/AnimateButton';
// import { fetcher } from 'utils/axios';

// // assets
// import { Eye, EyeSlash } from 'iconsax-react';

// // ============================|| JWT - LOGIN ||============================ //

// export default function AuthLogin({ forgot }) {
//   const [checked, setChecked] = useState(false);

//   const { isLoggedIn, login } = useAuth();
//   const scriptedRef = useScriptRef();

//   const [showPassword, setShowPassword] = useState(false);
//   const handleClickShowPassword = () => {
//     setShowPassword(!showPassword);
//   };

//   const handleMouseDownPassword = (event) => {
//     event.preventDefault();
//   };

//   return (
//     <>
// <Formik
//   initialValues={{
//     username: '<EMAIL>',
//     password: 'test123',
//     submit: null
//   }}
//   validationSchema={Yup.object().shape({
//     username: Yup.string().email('Must be a valid email').max(255).required('Email is required'),
//     password: Yup.string().max(255).required('Password is required')
//   })}
//   onSubmit={async (values, { setErrors, setStatus, setSubmitting }) => {
//     console.log(values);
//     try {
//       await login(values.username, values.password);
//       if (scriptedRef.current) {
//         setStatus({ success: true });
//         setSubmitting(false);
//         preload('api/menu/dashboard', fetcher); // load menu on login success
//       }
//     } catch (err) {
//       console.error(err);
//       if (scriptedRef.current) {
//         setStatus({ success: false });
//         setErrors({ submit: err.message });
//         setSubmitting(false);
//       }
//     }
//   }}
// >
//   {({ errors, handleBlur, handleChange, handleSubmit, isSubmitting, touched, values }) => (
//     <form noValidate onSubmit={handleSubmit}>
//       <Grid container spacing={3}>
//         <Grid item xs={12}>
//           <Stack spacing={1}>
//             <InputLabel htmlFor="username-login">Email Address</InputLabel>
//             <OutlinedInput
//               id="username-login"
//               type="email"
//               value={values.username}
//               name="username"
//               onBlur={handleBlur}
//               onChange={handleChange}
//               placeholder="Enter email address"
//               fullWidth
//               error={Boolean(touched.username && errors.username)}
//             />
//           </Stack>
//           {touched.username && errors.username && (
//             <FormHelperText error id="standard-weight-helper-text-username-login">
//               {errors.username}
//             </FormHelperText>
//           )}
//         </Grid>
//         <Grid item xs={12}>
//           <Stack spacing={1}>
//             <InputLabel htmlFor="password-login">Password</InputLabel>
//             <OutlinedInput
//               fullWidth
//               error={Boolean(touched.password && errors.password)}
//               id="password-login"
//               type={showPassword ? 'text' : 'password'}
//               value={values.password}
//               name="password"
//               onBlur={handleBlur}
//               onChange={handleChange}
//               endAdornment={
//                 <InputAdornment position="end">
//                   <IconButton
//                     aria-label="toggle password visibility"
//                     onClick={handleClickShowPassword}
//                     onMouseDown={handleMouseDownPassword}
//                     edge="end"
//                     color="secondary"
//                   >
//                     {showPassword ? <Eye /> : <EyeSlash />}
//                   </IconButton>
//                 </InputAdornment>
//               }
//               placeholder="Enter password"
//             />
//           </Stack>
//           {touched.password && errors.password && (
//             <FormHelperText error id="standard-weight-helper-text-password-login">
//               {errors.password}
//             </FormHelperText>
//           )}
//         </Grid>
//         <Grid item xs={12} sx={{ mt: -1 }}>
//           <Stack direction="row" justifyContent="space-between" alignItems="center" spacing={2}>
//             <FormControlLabel
//               control={
//                 <Checkbox
//                   checked={checked}
//                   onChange={(event) => setChecked(event.target.checked)}
//                   name="checked"
//                   color="primary"
//                   size="small"
//                 />
//               }
//               label={<Typography variant="h6">Keep me sign in</Typography>}
//             />
//             <Link variant="h6" component={RouterLink} to={isLoggedIn && forgot ? forgot : '/forgot-password'} color="text.primary">
//               Forgot Password?
//             </Link>
//           </Stack>
//         </Grid>
//         {errors.submit && (
//           <Grid item xs={12}>
//             <FormHelperText error>{errors.submit}</FormHelperText>
//           </Grid>
//         )}
//         <Grid item xs={12}>
//           <AnimateButton>
//             <Button disableElevation disabled={isSubmitting} fullWidth size="large" type="submit" variant="contained" color="primary">
//               Login
//             </Button>
//           </AnimateButton>
//         </Grid>
//       </Grid>
//     </form>
//   )}
// </Formik>

//     </>
//   );
// }

// AuthLogin.propTypes = { forgot: PropTypes.string };

import PropTypes from 'prop-types';
import { useState } from 'react';
import { Link as RouterLink } from 'react-router-dom';
import Grid from '@mui/material/Grid';
import Button from '@mui/material/Button';
import Checkbox from '@mui/material/Checkbox';
import Stack from '@mui/material/Stack';
import Link from '@mui/material/Link';
import InputLabel from '@mui/material/InputLabel';
import Typography from '@mui/material/Typography';
import OutlinedInput from '@mui/material/OutlinedInput';
import InputAdornment from '@mui/material/InputAdornment';
import FormHelperText from '@mui/material/FormHelperText';
import FormControlLabel from '@mui/material/FormControlLabel';
import * as Yup from 'yup';
import { Formik } from 'formik';
import useAuth from 'hooks/useAuth';
import useScriptRef from 'hooks/useScriptRef';
import IconButton from 'components/@extended/IconButton';
import AnimateButton from 'components/@extended/AnimateButton';
import { Eye, EyeSlash } from 'iconsax-react';

export default function AuthLogin({ forgot }) {
  const [checked, setChecked] = useState(false);
  const { isLoggedIn, login } = useAuth();
  const scriptedRef = useScriptRef();
  const [showPassword, setShowPassword] = useState(false);

  const handleClickShowPassword = () => {
    setShowPassword(!showPassword);
  };

  const handleMouseDownPassword = (event) => {
    event.preventDefault();
  };

  return (
    <>
      <Formik
        initialValues={{
          username: '',
          password: '',
          submit: null
        }}
        validationSchema={Yup.object().shape({
          username: Yup.string()
            .trim()
            .email('Must be a valid email')
            .max(255)
            .required('Email is required'),
          password: Yup.string().max(255).required('Password is required')
        })}
        onSubmit={async (values, { setErrors, setStatus, setSubmitting }) => {
          try {
            await login(values.username, values.password);
            if (scriptedRef.current) {
              setStatus({ success: true });
              setSubmitting(false);
            }
          } catch (err) {
            console.error(err);
            if (scriptedRef.current) {
              setStatus({ success: false });
              setErrors({ submit: err.message });
              setSubmitting(false);
            }
          }
        }}
      >
        {({ errors, handleBlur, handleChange, handleSubmit, isSubmitting, touched, values, isValid }) => (
          <form noValidate onSubmit={handleSubmit}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Stack spacing={1}>
                  <InputLabel htmlFor="username-login">Email Address</InputLabel>
                  <OutlinedInput
                    id="username-login"
                    type="email"
                    value={values.username}
                    name="username"
                    onBlur={(event) => {
                      handleChange(event);
                      event.target.value = event.target.value.trim(); // Trim spaces
                    }}
                    onChange={handleChange}
                    placeholder="<EMAIL>"
                    fullWidth
                    error={Boolean(touched.username && errors.username)}
                    inputProps={{
                      pattern: "^[^\\s]+(\\s+[^\\s]+)*$", // Pattern to prevent leading/trailing spaces
                    }}
                  />
                </Stack>
                {touched.username && errors.username && (
                  <FormHelperText error id="standard-weight-helper-text-username-login">
                    {errors.username}
                  </FormHelperText>
                )}
              </Grid>
              <Grid item xs={12}>
                <Stack spacing={1}>
                  <InputLabel htmlFor="password-login">Password</InputLabel>
                  <OutlinedInput
                    fullWidth
                    error={Boolean(touched.password && errors.password)}
                    id="password-login"
                    type={showPassword ? 'text' : 'password'}
                    value={values.password}
                    name="password"
                    onBlur={handleBlur}
                    onChange={handleChange}
                    onPaste={(e) => e.preventDefault()} // Disable paste
                    endAdornment={
                      <InputAdornment position="end">
                        <IconButton
                          aria-label="toggle password visibility"
                          onClick={handleClickShowPassword}
                          onMouseDown={handleMouseDownPassword}
                          edge="end"
                          color="secondary"
                        >
                          {showPassword ? <Eye /> : <EyeSlash />}
                        </IconButton>
                      </InputAdornment>
                    }
                    placeholder="Enter password"
                  />
                </Stack>
                {touched.password && errors.password && (
                  <FormHelperText error id="standard-weight-helper-text-password-login">
                    {errors.password}
                  </FormHelperText>
                )}
              </Grid>
              <Grid item xs={12} sx={{ mt: -1 }}>
                <Stack direction="row" justifyContent="space-between" alignItems="center" spacing={2}>
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={checked}
                        onChange={(event) => setChecked(event.target.checked)}
                        name="checked"
                        color="primary"
                        size="small"
                      />
                    }
                    label={<Typography variant="h6">Keep me signed in</Typography>}
                  />
                  <Link variant="h6" component={RouterLink} to={isLoggedIn && forgot ? forgot : '/forgot-password'} color="text.primary">
                    Forgot Password?
                  </Link>
                </Stack>
              </Grid>
              {errors.submit && (
                <Grid item xs={12}>
                  <FormHelperText error>{errors.submit}</FormHelperText>
                </Grid>
              )}
              <Grid item xs={12}>
                <AnimateButton>
                  <Button
                    disableElevation
                    disabled={isSubmitting || !isValid}
                    fullWidth
                    size="large"
                    type="submit"
                    variant="contained"
                    sx={{ backgroundColor: 'primary.main',  '&:hover': {
                      backgroundColor: 'primary.main'
                    }, }}
                  >
                    Login
                  </Button>
                </AnimateButton>
              </Grid>
            </Grid>
          </form>
        )}
      </Formik>
    </>
  );
}

AuthLogin.propTypes = { forgot: PropTypes.string };
