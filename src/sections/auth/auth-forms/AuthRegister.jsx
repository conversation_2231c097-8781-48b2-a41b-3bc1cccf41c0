// // import { useEffect, useState } from 'react';
// // import { Link as RouterLink, useNavigate } from 'react-router-dom';

// // // material-ui
// // import Box from '@mui/material/Box';
// // import Grid from '@mui/material/Grid';
// // import Button from '@mui/material/Button';
// // import FormControl from '@mui/material/FormControl';
// // import Stack from '@mui/material/Stack';
// // import Link from '@mui/material/Link';
// // import InputLabel from '@mui/material/InputLabel';
// // import Typography from '@mui/material/Typography';
// // import OutlinedInput from '@mui/material/OutlinedInput';
// // import InputAdornment from '@mui/material/InputAdornment';
// // import FormHelperText from '@mui/material/FormHelperText';

// // // third-party
// // import * as Yup from 'yup';
// // import { Formik } from 'formik';

// // // project-imports
// // import useAuth from 'hooks/useAuth';
// // import useScriptRef from 'hooks/useScriptRef';
// // import IconButton from 'components/@extended/IconButton';
// // import AnimateButton from 'components/@extended/AnimateButton';

// // import { openSnackbar } from 'api/snackbar';
// // import { strengthColor, strengthIndicator } from 'utils/password-strength';

// // // assets
// // import { Eye, EyeSlash } from 'iconsax-react';

// // // ============================|| JWT - REGISTER ||============================ //

// // export default function AuthRegister() {
// //   const { register } = useAuth();
// //   const scriptedRef = useScriptRef();
// //   const navigate = useNavigate();

// //   const [level, setLevel] = useState();
// //   const [showPassword, setShowPassword] = useState(false);
// //   const handleClickShowPassword = () => {
// //     setShowPassword(!showPassword);
// //   };

// //   const handleMouseDownPassword = (event) => {
// //     event.preventDefault();
// //   };

// //   const changePassword = (value) => {
// //     const temp = strengthIndicator(value);
// //     setLevel(strengthColor(temp));
// //   };

// //   useEffect(() => {
// //     changePassword('');
// //   }, []);

// //   return (
// //     <>
// //       <Formik
// //         initialValues={{
// //           firstname: '',
// //           lastname: '',
// //           email: '',
// //           username: '',
// //           password: '',
// //           submit: null
// //         }}
// //         validationSchema={Yup.object().shape({
// //           firstname: Yup.string().max(255).required('First Name is required'),
// //           lastname: Yup.string().max(255).required('Last Name is required'),
// //           email: Yup.string().email('Must be a valid email').max(255).required('Email is required'),
// //           password: Yup.string().max(255).required('Password is required')
// //         })}
// //         onSubmit={async (values, { setErrors, setStatus, setSubmitting }) => {
// //           try {
// //             await register(values.email, values.password, values.firstname, values.lastname);
// //             if (scriptedRef.current) {
// //               setStatus({ success: true });
// //               setSubmitting(false);
// //               openSnackbar({
// //                 open: true,
// //                 message: 'Your registration has been successfully completed.',
// //                 variant: 'alert',

// //                 alert: {
// //                   color: 'success'
// //                 }
// //               });
// //               console.log('Form values:', values);
// //               setTimeout(() => {
// //                 navigate('/login', { replace: true });
// //               }, 1500);
// //             }
// //           } catch (err) {
// //             console.error(err);
// //             if (scriptedRef.current) {
// //               setStatus({ success: false });
// //               setErrors({ submit: err.message });
// //               setSubmitting(false);
// //             }
// //           }
// //         }}
// //       >
// //         {({ errors, handleBlur, handleChange, handleSubmit, isSubmitting, touched, values }) => (
// //           <form noValidate onSubmit={handleSubmit}>
// //             <Grid container spacing={3}>
// //               <Grid item xs={12} md={6}>
// //                 <Stack spacing={1}>
// //                   <InputLabel htmlFor="firstname-signup">First Name*</InputLabel>
// //                   <OutlinedInput
// //                     id="firstname-login"
// //                     type="firstname"
// //                     value={values.firstname}
// //                     name="firstname"
// //                     onBlur={handleBlur}
// //                     onChange={handleChange}
// //                     placeholder="John"
// //                     fullWidth
// //                     error={Boolean(touched.firstname && errors.firstname)}
// //                   />
// //                 </Stack>
// //                 {touched.firstname && errors.firstname && (
// //                   <FormHelperText error id="helper-text-firstname-signup">
// //                     {errors.firstname}
// //                   </FormHelperText>
// //                 )}
// //               </Grid>
// //               <Grid item xs={12} md={6}>
// //                 <Stack spacing={1}>
// //                   <InputLabel htmlFor="lastname-signup">Last Name*</InputLabel>
// //                   <OutlinedInput
// //                     fullWidth
// //                     error={Boolean(touched.lastname && errors.lastname)}
// //                     id="lastname-signup"
// //                     type="lastname"
// //                     value={values.lastname}
// //                     name="lastname"
// //                     onBlur={handleBlur}
// //                     onChange={handleChange}
// //                     placeholder="Doe"
// //                     inputProps={{}}
// //                   />
// //                 </Stack>
// //                 {touched.lastname && errors.lastname && (
// //                   <FormHelperText error id="helper-text-lastname-signup">
// //                     {errors.lastname}
// //                   </FormHelperText>
// //                 )}
// //               </Grid>
// //               <Grid item xs={12}>
// //                 <Stack spacing={1}>
// //                   <InputLabel htmlFor="company-signup">Company</InputLabel>
// //                   <OutlinedInput
// //                     fullWidth
// //                     error={Boolean(touched.company && errors.company)}
// //                     id="company-signup"
// //                     value={values.company}
// //                     name="company"
// //                     onBlur={handleBlur}
// //                     onChange={handleChange}
// //                     placeholder="Demo Inc."
// //                     inputProps={{}}
// //                   />
// //                 </Stack>
// //                 {touched.company && errors.company && (
// //                   <FormHelperText error id="helper-text-company-signup">
// //                     {errors.company}
// //                   </FormHelperText>
// //                 )}
// //               </Grid>
// //               <Grid item xs={12}>
// //                 <Stack spacing={1}>
// //                   <InputLabel htmlFor="email-signup">Email Address*</InputLabel>
// //                   <OutlinedInput
// //                     fullWidth
// //                     error={Boolean(touched.email && errors.email)}
// //                     id="email-login"
// //                     type="email"
// //                     value={values.email}
// //                     name="email"
// //                     onBlur={handleBlur}
// //                     onChange={handleChange}
// //                     placeholder="<EMAIL>"
// //                     inputProps={{}}
// //                   />
// //                 </Stack>
// //                 {touched.email && errors.email && (
// //                   <FormHelperText error id="helper-text-email-signup">
// //                     {errors.email}
// //                   </FormHelperText>
// //                 )}
// //               </Grid>
// //               <Grid item xs={12}>
// //                 <Stack spacing={1}>
// //                   <InputLabel htmlFor="password-signup">Password</InputLabel>
// //                   <OutlinedInput
// //                     fullWidth
// //                     error={Boolean(touched.password && errors.password)}
// //                     id="password-signup"
// //                     type={showPassword ? 'text' : 'password'}
// //                     value={values.password}
// //                     name="password"
// //                     onBlur={handleBlur}
// //                     onChange={(e) => {
// //                       handleChange(e);
// //                       changePassword(e.target.value);
// //                     }}
// //                     endAdornment={
// //                       <InputAdornment position="end">
// //                         <IconButton
// //                           aria-label="toggle password visibility"
// //                           onClick={handleClickShowPassword}
// //                           onMouseDown={handleMouseDownPassword}
// //                           edge="end"
// //                           color="secondary"
// //                         >
// //                           {showPassword ? <Eye /> : <EyeSlash />}
// //                         </IconButton>
// //                       </InputAdornment>
// //                     }
// //                     placeholder="******"
// //                     inputProps={{}}
// //                   />
// //                 </Stack>
// //                 {touched.password && errors.password && (
// //                   <FormHelperText error id="helper-text-password-signup">
// //                     {errors.password}
// //                   </FormHelperText>
// //                 )}
// //                 <FormControl fullWidth sx={{ mt: 2 }}>
// //                   <Grid container spacing={2} alignItems="center">
// //                     <Grid item>
// //                       <Box sx={{ bgcolor: level?.color, width: 85, height: 8, borderRadius: '7px' }} />
// //                     </Grid>
// //                     <Grid item>
// //                       <Typography variant="subtitle1" fontSize="0.75rem">
// //                         {level?.label}
// //                       </Typography>
// //                     </Grid>
// //                   </Grid>
// //                 </FormControl>
// //               </Grid>
// //               <Grid item xs={12}>
// //                 <Typography variant="body2">
// //                   By Signing up, you agree to our &nbsp;
// //                   <Link variant="subtitle2" component={RouterLink} to="#">
// //                     Terms of Service
// //                   </Link>
// //                   &nbsp; and &nbsp;
// //                   <Link variant="subtitle2" component={RouterLink} to="#">
// //                     Privacy Policy
// //                   </Link>
// //                 </Typography>
// //               </Grid>
// //               {errors.submit && (
// //                 <Grid item xs={12}>
// //                   <FormHelperText error>{errors.submit}</FormHelperText>
// //                 </Grid>
// //               )}
// //               <Grid item xs={12}>
// //                 <AnimateButton>
// //                   <Button disableElevation disabled={isSubmitting} fullWidth size="large" type="submit" variant="contained" color="primary">
// //                     Create Account
// //                   </Button>
// //                 </AnimateButton>
// //               </Grid>
// //             </Grid>
// //           </form>
// //         )}
// //       </Formik>
// //     </>
// //   );
// // }





// import { useEffect, useState } from 'react';
// import { Link as RouterLink, useNavigate } from 'react-router-dom';

// // material-ui
// import Box from '@mui/material/Box';
// import Grid from '@mui/material/Grid';
// import Button from '@mui/material/Button';
// import FormControl from '@mui/material/FormControl';
// import Stack from '@mui/material/Stack';
// import Link from '@mui/material/Link';
// import InputLabel from '@mui/material/InputLabel';
// import Typography from '@mui/material/Typography';
// import OutlinedInput from '@mui/material/OutlinedInput';
// import InputAdornment from '@mui/material/InputAdornment';
// import FormHelperText from '@mui/material/FormHelperText';

// // third-party
// import * as Yup from 'yup';
// import { Formik } from 'formik';

// // project-imports
// import useAuth from 'hooks/useAuth';
// import useScriptRef from 'hooks/useScriptRef';
// import IconButton from 'components/@extended/IconButton';
// import AnimateButton from 'components/@extended/AnimateButton';

// import { openSnackbar } from 'api/snackbar';
// import { strengthColor, strengthIndicator } from 'utils/password-strength';

// // assets
// import { Eye, EyeSlash } from 'iconsax-react';

// // ============================|| JWT - REGISTER ||============================ //

// export default function AuthRegister() {
//   const { register } = useAuth();
//   const scriptedRef = useScriptRef();
//   const navigate = useNavigate();

//   const [level, setLevel] = useState();
//   const [showPassword, setShowPassword] = useState(false);
//   const handleClickShowPassword = () => {
//     setShowPassword(!showPassword);
//   };

//   const handleMouseDownPassword = (event) => {
//     event.preventDefault();
//   };

//   const changePassword = (value) => {
//     const temp = strengthIndicator(value);
//     setLevel(strengthColor(temp));
//   };

//   useEffect(() => {
//     changePassword('');
//   }, []);

//   return (
//     <>
//       <Formik
//         initialValues={{
//           firstname: '',
//           lastname: '',
//           email: '',
//           username: '',
//           password: '',
//           submit: null
//         }}
//         validationSchema={Yup.object().shape({
//           firstname: Yup.string().max(255).required('First Name is required'),
//           lastname: Yup.string().max(255).required('Last Name is required'),
//           email: Yup.string().email('Must be a valid email').max(255).required('Email is required'),
//           password: Yup.string().max(255).required('Password is required')
//         })}
//         onSubmit={async (values, { setErrors, setStatus, setSubmitting }) => {
//           try {
//             await register(values.email, values.password, values.firstname, values.lastname);
//             if (scriptedRef.current) {
//               setStatus({ success: true });
//               setSubmitting(false);
//               openSnackbar({
//                 open: true,
//                 message: 'Your registration has been successfully completed.',
//                 variant: 'alert',

//                 alert: {
//                   color: 'success'
//                 }
//               });
//               console.log('Form values:', values);
//               setTimeout(() => {
//                 navigate('/login', { replace: true });
//               }, 1500);
//             }
//           } catch (err) {
//             console.error(err);
//             if (scriptedRef.current) {
//               setStatus({ success: false });
//               setErrors({ submit: err.message });
//               setSubmitting(false);
//             }
//           }
//         }}
//       >
//         {({ errors, handleBlur, handleChange, handleSubmit, isSubmitting, touched, values }) => (
//           <form noValidate onSubmit={handleSubmit}>
//             <Grid container spacing={3}>
//               <Grid item xs={12} md={6}>
//                 <Stack spacing={1}>
//                   <InputLabel htmlFor="firstname-signup">First Name*</InputLabel>
//                   <OutlinedInput
//                     id="firstname-login"
//                     type="firstname"
//                     value={values.firstname}
//                     name="firstname"
//                     onBlur={handleBlur}
//                     onChange={handleChange}
//                     placeholder="John"
//                     fullWidth
//                     error={Boolean(touched.firstname && errors.firstname)}
//                   />
//                 </Stack>
//                 {touched.firstname && errors.firstname && (
//                   <FormHelperText error id="helper-text-firstname-signup">
//                     {errors.firstname}
//                   </FormHelperText>
//                 )}
//               </Grid>
//               <Grid item xs={12} md={6}>
//                 <Stack spacing={1}>
//                   <InputLabel htmlFor="lastname-signup">Last Name*</InputLabel>
//                   <OutlinedInput
//                     fullWidth
//                     error={Boolean(touched.lastname && errors.lastname)}
//                     id="lastname-signup"
//                     type="lastname"
//                     value={values.lastname}
//                     name="lastname"
//                     onBlur={handleBlur}
//                     onChange={handleChange}
//                     placeholder="Doe"
//                     inputProps={{}}
//                   />
//                 </Stack>
//                 {touched.lastname && errors.lastname && (
//                   <FormHelperText error id="helper-text-lastname-signup">
//                     {errors.lastname}
//                   </FormHelperText>
//                 )}
//               </Grid>
//               <Grid item xs={12}>
//                 <Stack spacing={1}>
//                   <InputLabel htmlFor="company-signup">Role</InputLabel>
//                   <OutlinedInput
//                     fullWidth
//                     error={Boolean(touched.company && errors.company)}
//                     id="company-signup"
//                     value={values.company}
//                     name="role"
//                     onBlur={handleBlur}
//                     onChange={handleChange}
//                     placeholder="Demo Inc."
//                     inputProps={{}}
//                   />
//                 </Stack>
//                 {touched.company && errors.company && (
//                   <FormHelperText error id="helper-text-company-signup">
//                     {errors.company}
//                   </FormHelperText>
//                 )}
//               </Grid>
//               <Grid item xs={12}>
//                 <Stack spacing={1}>
//                   <InputLabel htmlFor="email-signup">Email Address*</InputLabel>
//                   <OutlinedInput
//                     fullWidth
//                     error={Boolean(touched.email && errors.email)}
//                     id="email-login"
//                     type="email"
//                     value={values.email}
//                     name="email"
//                     onBlur={handleBlur}
//                     onChange={handleChange}
//                     placeholder="<EMAIL>"
//                     inputProps={{}}
//                   />
//                 </Stack>
//                 {touched.email && errors.email && (
//                   <FormHelperText error id="helper-text-email-signup">
//                     {errors.email}
//                   </FormHelperText>
//                 )}
//               </Grid>
//               <Grid item xs={12}>
//                 <Stack spacing={1}>
//                   <InputLabel htmlFor="password-signup">Password</InputLabel>
//                   <OutlinedInput
//                     fullWidth
//                     error={Boolean(touched.password && errors.password)}
//                     id="password-signup"
//                     type={showPassword ? 'text' : 'password'}
//                     value={values.password}
//                     name="password"
//                     onBlur={handleBlur}
//                     onChange={(e) => {
//                       handleChange(e);
//                       changePassword(e.target.value);
//                     }}
//                     endAdornment={
//                       <InputAdornment position="end">
//                         <IconButton
//                           aria-label="toggle password visibility"
//                           onClick={handleClickShowPassword}
//                           onMouseDown={handleMouseDownPassword}
//                           edge="end"
//                           color="secondary"
//                         >
//                           {showPassword ? <Eye /> : <EyeSlash />}
//                         </IconButton>
//                       </InputAdornment>
//                     }
//                     placeholder="******"
//                     inputProps={{}}
//                   />
//                 </Stack>
//                 {touched.password && errors.password && (
//                   <FormHelperText error id="helper-text-password-signup">
//                     {errors.password}
//                   </FormHelperText>
//                 )}
//                 <FormControl fullWidth sx={{ mt: 2 }}>
//                   <Grid container spacing={2} alignItems="center">
//                     <Grid item>
//                       <Box sx={{ bgcolor: level?.color, width: 85, height: 8, borderRadius: '7px' }} />
//                     </Grid>
//                     <Grid item>
//                       <Typography variant="subtitle1" fontSize="0.75rem">
//                         {level?.label}
//                       </Typography>
//                     </Grid>
//                   </Grid>
//                 </FormControl>
//               </Grid>
//               <Grid item xs={12}>
//                 <Typography variant="body2">
//                   By Signing up, you agree to our &nbsp;
//                   <Link variant="subtitle2" component={RouterLink} to="#">
//                     Terms of Service
//                   </Link>
//                   &nbsp; and &nbsp;
//                   <Link variant="subtitle2" component={RouterLink} to="#">
//                     Privacy Policy
//                   </Link>
//                 </Typography>
//               </Grid>
//               {errors.submit && (
//                 <Grid item xs={12}>
//                   <FormHelperText error>{errors.submit}</FormHelperText>
//                 </Grid>
//               )}
//               <Grid item xs={12}>
//                 <AnimateButton>
//                   <Button disableElevation disabled={isSubmitting} fullWidth size="large" type="submit" variant="contained" color="primary">
//                     Create Account
//                   </Button>
//                 </AnimateButton>
//               </Grid>
//             </Grid>
//           </form>
//         )}
//       </Formik>
//     </>
//   );
// }


// this code is working for registration

import { useEffect, useState } from 'react';
import { Link as RouterLink, useNavigate } from 'react-router-dom';

// material-ui
import Box from '@mui/material/Box';
import Grid from '@mui/material/Grid';
import Button from '@mui/material/Button';
import FormControl from '@mui/material/FormControl';
import Stack from '@mui/material/Stack';
import Link from '@mui/material/Link';
import InputLabel from '@mui/material/InputLabel';
import Typography from '@mui/material/Typography';
import OutlinedInput from '@mui/material/OutlinedInput';
import InputAdornment from '@mui/material/InputAdornment';
import FormHelperText from '@mui/material/FormHelperText';

// third-party
import * as Yup from 'yup';
import { Formik } from 'formik';

// project-imports
import useAuth from 'hooks/useAuth';
// import useScriptRef from 'hooks/useScriptRef';
import useScriptRef from 'hooks/useScriptRef';
import IconButton from 'components/@extended/IconButton';
import AnimateButton from 'components/@extended/AnimateButton';

import { openSnackbar } from 'api/snackbar';
import { strengthColor, strengthIndicator } from 'utils/password-strength';

// assets
import { Eye, EyeSlash } from 'iconsax-react';

// ============================|| JWT - REGISTER ||============================ //

export default function AuthRegister() {
  const { register } = useAuth();
  const scriptedRef = useScriptRef();
  const navigate = useNavigate();

  const [level, setLevel] = useState();
  const [showPassword, setShowPassword] = useState(false);
  const handleClickShowPassword = () => {
    setShowPassword(!showPassword);
  };

  const handleMouseDownPassword = (event) => {
    event.preventDefault();
  };

  const changePassword = (value) => {
    const temp = strengthIndicator(value);
    setLevel(strengthColor(temp));
  };

  useEffect(() => {
    changePassword('');
  }, []);
  



  return (
    <>
      <Formik
        initialValues={{
          first_name: '',  // Updated field
          last_name: '',   // Updated field
          email: '',
          username: '',
          role: '',
          password: '',
          submit: null
        }}
        validationSchema={Yup.object().shape({
          first_name: Yup.string().max(255).required('First Name is required'),  // Updated field
          last_name: Yup.string().max(255).required('Last Name is required'),    // Updated field
          email: Yup.string().email('Must be a valid email').max(255).required('Email is required'),
          username: Yup.string().max(255).required('Username is required'),
          role: Yup.string().max(255).required('Role is required'),
          password: Yup.string()
            .matches(
              /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[A-Za-z\d@$!%*?&]{8,}$/,
              "Password must contain at least one lowercase letter, one uppercase letter, one number, and be at least 8 characters long"
            )
            .required('Password is required')

        })}
        // onSubmit={async (values, { setErrors, setStatus, setSubmitting }) => {
        //   try {
        //     await register(values.username, values.email, values.password, values.first_name, values.last_name, values.role);  // Updated fields
        //     if (scriptedRef.current) {
        //       setStatus({ success: true });
        //       setSubmitting(false);
        //       openSnackbar({
        //         open: true,
        //         message: 'Your registration has been successfully completed.',
        //         variant: 'alert',
        //         alert: {
        //           color: 'success'
        //         }
        //       });
        //       console.log('Form values:', values);
        //       setTimeout(() => {
        //         navigate('/login', { replace: true });
        //       }, 1500);
        //     }
        //   } catch (err) {
        //     console.error(err);
        //     if (scriptedRef.current) {
        //       setStatus({ success: false });
        //       setErrors({ submit: err.message });
        //       setSubmitting(false);
        //     }
        //   }
        // }}
        // onSubmit={async (values, { setErrors, setStatus, setSubmitting }) => {
        //   console.log('Submitting form values:', values); // Log the form values at the start
        
        //   try {
        //     console.log('Attempting to register the user...');
        //     await register(values.username, values.email, values.password, values.first_name, values.last_name, values.role);  // Updated fields
        //     console.log('User registered successfully');
        //     console.log('scriptedRef.current:', scriptedRef.current);

        //     if (scriptedRef.current) {
        //       setStatus({ success: true });
        //       setSubmitting(false);
        
        //       console.log('Showing success snackbar...');
        //       openSnackbar({
        //         open: true,
        //         message: 'Your registration has been successfully completed.',
        //         variant: 'alert',
        //         alert: {
        //           color: 'success'
        //         }
        //       });
        
        //       console.log('Form submission successful, navigating to login...');
        //       setTimeout(() => {
        //         navigate('/login', { replace: true });
        //       }, 1500);
        //     }
        //   } catch (err) {
        //     console.error('Error occurred during registration:', err);
        
        //     if (scriptedRef.current) {
        //       console.log('Handling error, setting error status...');
        //       setStatus({ success: false });
        //       setErrors({ submit: err.message });
        //       setSubmitting(false);
        //     }
        //   }
        // }}
        onSubmit={async (values, { setErrors, setStatus, setSubmitting }) => {
          console.log('Submitting form values:', values); // Log the form values at the start
        
          try {
            console.log('Attempting to register the user...');
            await register(values.username, values.email, values.password, values.first_name, values.last_name, values.role);
        
            console.log('User registered successfully');
            console.log('scriptedRef.current:', scriptedRef.current);
        
            if (scriptedRef.current) {
              setStatus({ success: true });
              setSubmitting(false);
        
              openSnackbar({
                open: true,
                message: 'Your registration has been successfully completed.',
                variant: 'alert',
                alert: {
                  color: 'success'
                }
              });
        
              setTimeout(() => {
                navigate('/login', { replace: true });
              }, 1500);
            }
          } catch (err) {
            console.error('Error occurred during registration:', err);
        
            if (scriptedRef.current) {
              setStatus({ success: false });
              setSubmitting(false);
        
              // Check if the error is related to an existing email
              if (err.response && err.response.status === 400) {
                setErrors({ email: 'This email is already registered. Please use a different email.' });
              } else {
                setErrors({ submit: err.message });
              }
            }
          }
        }}
        
      >
        {({ errors, handleBlur, handleChange, handleSubmit, isSubmitting, touched, values }) => (
          <form noValidate onSubmit={handleSubmit}>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Stack spacing={1}>
                  <InputLabel htmlFor="first_name-signup">First Name*</InputLabel>
                  <OutlinedInput
                    id="first_name-signup"
                    type="text"
                    value={values.first_name}
                    name="first_name"
                    onBlur={(event) => {
                      event.target.value = event.target.value.trim();
                      handleBlur(event);
                    }}
                    onChange={handleChange}
                    onKeyDown={(event) => {
                      if (!isNaN(event.key) || event.key === ' ') {
                        event.preventDefault();
                      }
                    }}
                    placeholder="John"
                    fullWidth
                    error={Boolean(touched.first_name && errors.first_name)}
                  />
                </Stack>
                {touched.first_name && errors.first_name && (
                  <FormHelperText error id="helper-text-first_name-signup">
                    {errors.first_name}
                  </FormHelperText>
                )}
            </Grid>

            <Grid item xs={12} md={6}>
              <Stack spacing={1}>
                <InputLabel htmlFor="last_name-signup">Last Name*</InputLabel>
                  <OutlinedInput
                    id="last_name-signup"
                    type="text"
                    value={values.last_name}
                    name="last_name"
                    onBlur={(event) => {
                      event.target.value = event.target.value.trim();
                      handleBlur(event);
                    }}
                    onChange={handleChange}
                    onKeyDown={(event) => {
                      if (!isNaN(event.key) || event.key === ' ') {
                        event.preventDefault();
                      }
                    }}
                    placeholder="Doe"
                    fullWidth
                    error={Boolean(touched.last_name && errors.last_name)}
                  />
              </Stack>
                {touched.last_name && errors.last_name && (
                  <FormHelperText error id="helper-text-last_name-signup">
                    {errors.last_name}
                  </FormHelperText>
                )}
            </Grid>

              <Grid item xs={12}>
                <Stack spacing={1}>
                  <InputLabel htmlFor="username-signup">Username*</InputLabel>
                  <OutlinedInput
                    fullWidth
                    error={Boolean(touched.username && errors.username)}
                    id="username-signup"
                    type="text"
                    value={values.username}
                    name="username"
                    onBlur={(event) => {
                      event.target.value = event.target.value.trim();
                      handleBlur(event);
                    }}
                    onChange={(event) => {
                      // Prevent leading space and set the value
                      const inputValue = event.target.value;
                      if (inputValue.length === 0 || inputValue[0] !== ' ') {
                        handleChange(event);
                      }
                    }}
                    onKeyDown={(event) => {
                      // Prevent leading space
                      if (event.key === ' ' && event.target.selectionStart === 0) {
                        event.preventDefault();
                      }
                    }}
                    placeholder="john_doe"
                    inputProps={{}}
                  />
                </Stack>
                {touched.username && errors.username && (
                  <FormHelperText error id="helper-text-username-signup">
                    {errors.username}
                  </FormHelperText>
                )}
              </Grid>

              <Grid item xs={12}>
              <Stack spacing={1}>
                <InputLabel htmlFor="role-signup">Role*</InputLabel>
                <OutlinedInput
                  fullWidth
                  error={Boolean(touched.role && errors.role)}
                  id="role-signup"
                  type="text"
                  value={values.role}
                  name="role"
                  onBlur={(event) => {
                    event.target.value = event.target.value.trim();
                    handleBlur(event);
                  }}
                  onChange={(event) => {
                    const inputValue = event.target.value;
                    // Allow only alphabetic characters and spaces, and prevent leading space
                    if (/^[A-Za-z\s]*$/.test(inputValue) && (inputValue.length === 0 || inputValue[0] !== ' ')) {
                      handleChange(event);
                    }
                  }}
                  onKeyDown={(event) => {
                    // Prevent space at the start of input
                    if (event.key === ' ' && event.target.selectionStart === 0) {
                      event.preventDefault();
                    }
                  }}
                  placeholder="Developer"
                  inputProps={{}}
                />
              </Stack>
              {touched.role && errors.role && (
                <FormHelperText error id="helper-text-role-signup">
                  {errors.role}
                </FormHelperText>
              )}
            </Grid>

              <Grid item xs={12}>
                <Stack spacing={1}>
                  <InputLabel htmlFor="email-signup">Email Address*</InputLabel>
                  <OutlinedInput
                    fullWidth
                    error={Boolean(touched.email && errors.email)}
                    id="email-signup"
                    type="email"
                    value={values.email}
                    name="email"
                    onBlur={handleBlur}
                    onChange={handleChange}
                    placeholder="<EMAIL>"
                    inputProps={{}}
                  />
                </Stack>
                {touched.email && errors.email && (
                  <FormHelperText error id="helper-text-email-signup">
                    {errors.email}
                  </FormHelperText>
                )}
              </Grid>

              <Grid item xs={12}>
                <Stack spacing={1}>
                  <InputLabel htmlFor="password-signup">Password*</InputLabel>
                  <OutlinedInput
                    fullWidth
                    error={Boolean(touched.password && errors.password)}
                    id="password-signup"
                    type={showPassword ? 'text' : 'password'}
                    value={values.password}
                    name="password"
                    onBlur={handleBlur}
                    onChange={(e) => {
                      handleChange(e);
                      changePassword(e.target.value);  // Optional if you have a function for checking password strength
                    }}
                    endAdornment={
                      <InputAdornment position="end">
                        <IconButton
                          aria-label="toggle password visibility"
                          onClick={handleClickShowPassword}
                          onMouseDown={handleMouseDownPassword}
                          edge="end"
                          color="secondary"
                        >
                          {showPassword ? <Eye /> : <EyeSlash />}
                        </IconButton>
                      </InputAdornment>
                    }
                    placeholder="******"
                  />
                </Stack>
                {touched.password && errors.password && (
                  <FormHelperText error id="helper-text-password-signup">
                    {errors.password}
                  </FormHelperText>
                )}
                <FormControl fullWidth sx={{ mt: 2 }}>
                  <Grid container spacing={2} alignItems="center">
                    <Grid item>
                      <Box sx={{ bgcolor: level?.color, width: 85, height: 8, borderRadius: '7px' }} />
                    </Grid>
                    <Grid item>
                      <Typography variant="subtitle1" fontSize="0.75rem">
                        {level?.label}
                      </Typography>
                    </Grid>
                  </Grid>
                </FormControl>
              </Grid>

              <Grid item xs={12}>
                <Typography variant="body2">
                  By Signing up, you agree to our &nbsp;
                  <Link variant="subtitle2" component={RouterLink} to="#">
                    Terms of Service
                  </Link>
                  &nbsp; and &nbsp;
                  <Link variant="subtitle2" component={RouterLink} to="#">
                    Privacy Policy
                  </Link>
                </Typography>
              </Grid>
              {errors.submit && (
                <Grid item xs={12}>
                  <FormHelperText error>{errors.submit}</FormHelperText>
                </Grid>
              )}
              <Grid item xs={12}>
                <AnimateButton>
                  <Button disableElevation disabled={isSubmitting} fullWidth size="large" type="submit" variant="contained" sx={{ backgroundColor: "primary.main", color: "white",'&:hover': {
                backgroundColor: "primary.main" 
              }, }}>
                    Create Account
                  </Button>
                </AnimateButton>
              </Grid>
            </Grid>
          </form>
        )}
      </Formik>
    </>
  );
}
