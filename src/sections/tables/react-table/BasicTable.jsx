// src/sections/tables/react-table/BasicTable.jsx

import React from 'react';
import { useTable, flexRender, getCoreRowModel } from '@tanstack/react-table';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import ScrollX from '../../components/ScrollX';
import MainCard from '../../components/MainCard';
import { CSVExport } from '../../components/third-party/react-table'; // Corrected path
import LinearWithLabel from '../../components/@extended/progress/LinearWithLabel';
import makeData from '../../data/react-table';

// Example data and columns
const data = makeData(); // or provide your own data

const columns = [
  // Your column definitions here
];

export default function BasicTable() {
  const table = useTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel()
  });

  return (
    <MainCard title="Basic Table">
      <ScrollX>
        <Table>
          <TableHead>
            {table.getHeaderGroups().map(headerGroup => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map(header => (
                  <TableCell key={header.id}>
                    {flexRender(header.column.columnDef.header, header.getContext())}
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableHead>
          <TableBody>
            {table.getRowModel().rows.map(row => (
              <TableRow key={row.id}>
                {row.getVisibleCells().map(cell => (
                  <TableCell key={cell.id}>
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </ScrollX>
      <CSVExport data={data} columns={columns} />
    </MainCard>
  );
}
