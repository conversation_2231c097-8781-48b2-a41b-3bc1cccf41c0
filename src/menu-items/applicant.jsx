// This is example of menu item without group for horizontal layout. There will be no children.

// third-party
import { FormattedMessage } from 'react-intl';

// assets
import { DocumentCode2, Home} from 'iconsax-react';

// type

// icons
const icons = {
  applyjob: Home
};

// ==============================|| MENU ITEMS - SAMPLE PAGE ||============================== //

const applyjob = {
  id: 'group-inputs',
  title: <FormattedMessage id="Applicants" />,
  type: 'group',
  url: '/applyjob',
  icon: icons.applyjob
};

export default applyjob;
