// This is example of menu item without group for horizontal layout. There will be no children.

// third-party
import { FormattedMessage } from 'react-intl';

// assets
import { DocumentCode2, Graph, People} from 'iconsax-react';
import ReceiptLongOutlinedIcon from '@mui/icons-material/ReceiptLongOutlined';
// type

// icons
const icons = {
  samplePage: ReceiptLongOutlinedIcon
};

// ==============================|| MENU ITEMS - SAMPLE PAGE ||============================== //

const job_requisition_page = {
  id: 'requisition-inputs',
  title: <FormattedMessage id="Job Requisition" />,
  type: 'group',
  url: '/job_requisition',
  icon: icons.samplePage
};

export default job_requisition_page;
