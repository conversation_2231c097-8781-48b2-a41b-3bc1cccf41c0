// This is example of menu item without group for horizontal layout. There will be no children.

// third-party
import { FormattedMessage } from 'react-intl';

// assets
import { DocumentCode2, Graph, People} from 'iconsax-react';

// type

// icons
const icons = {
  samplePage: People
};

// ==============================|| MENU ITEMS - SAMPLE PAGE ||============================== //

const client_page = {
  id: 'group-pages',
  // title: 'pages',
  type: 'group',  
  children: [
    {
      id: 'client',
      title: 'Clients',
      type: 'collapse',
      icon: icons.samplePage,
      children: [
        {
          id: 'Client',
          title: 'Client',
          type: 'item',
          url: '/clientpage',
          // icon: icons.samplePage,
          // target: true
        },
    
          {
            id: 'Client Contacts',
            title: 'Client Contacts',
            type: 'item',
            url: '/jobrequest',
            // icon: icons.samplePage,
            // target: true
          },

      ]
    },
    
  ]
};

export default client_page;
