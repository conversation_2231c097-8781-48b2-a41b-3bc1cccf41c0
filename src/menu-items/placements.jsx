// This is example of menu item without group for horizontal layout. There will be no children.

// third-party
import { FormattedMessage } from 'react-intl';

// assets
import { DocumentCode2, Graph, People} from 'iconsax-react';
import WhereToVoteIcon from '@mui/icons-material/WhereToVote';
// type

// icons
const icons = {
  samplePage: WhereToVoteIcon
};

// ==============================|| MENU ITEMS - SAMPLE PAGE ||============================== //

const placements = {
  id: 'placement',
  title: <FormattedMessage id="Placement" />,
  type: 'group',
  url: '/placements',
  icon: icons.samplePage
};

export default placements;
