// This is example of menu item without group for horizontal layout. There will be no children.

// third-party
import { FormattedMessage } from 'react-intl';

// assets
import { DocumentCode2, Graph, People} from 'iconsax-react';
import AddBusinessIcon from '@mui/icons-material/AddBusiness';
// type

// icons
const icons = {
  samplePage: AddBusinessIcon
};

// ==============================|| MENU ITEMS - SAMPLE PAGE ||============================== //

const vendors = {
  id: 'vendor-group',
  title: <FormattedMessage id="Vendor" />,
  type: 'group',
  url: '/vendor',
  icon: icons.samplePage
};

export default vendors;
