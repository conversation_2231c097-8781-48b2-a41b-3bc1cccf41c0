// This is example of menu item without group for horizontal layout. There will be no children.

// third-party
import { FormattedMessage } from 'react-intl';

// assets
import { DocumentCode2, Graph, People} from 'iconsax-react';
import EventSeatIcon from '@mui/icons-material/EventSeat';
// type

// icons
const icons = {
  samplePage: EventSeatIcon
};

// ==============================|| MENU ITEMS - SAMPLE PAGE ||============================== //

const talent_bench = {
  id: 'talent-group',
  title: <FormattedMessage id="Talent Bench" />,
  type: 'group',
  url: '/talent-bench',
  icon: icons.samplePage
};

export default talent_bench;
