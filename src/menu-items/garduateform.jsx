// This is example of menu item without group for horizontal layout. There will be no children.

// third-party
import { FormattedMessage } from 'react-intl';

// assets
import { DocumentCode2, Home} from 'iconsax-react';
import { padding } from '@mui/system';

// type

// icons
const icons = {
  applyjob: Home
};

// ==============================|| MENU ITEMS - SAMPLE PAGE ||============================== //

const graduateform = {
  id: 'Graduate Form',
  title: <FormattedMessage id="Graduate Form "/>,
  type: 'group',
  url: '/graduate-form',
  icon: icons.applyjob,
};

export default graduateform ;
