// This is example of menu item without group for horizontal layout. There will be no children.

// third-party
import { FormattedMessage } from 'react-intl';

// assets
import { DocumentCode2, Home, KyberNetwork } from 'iconsax-react';

// type
// import PeopleAltIcon from '@mui/icons-material/PeopleAlt';
import PersonSearchIcon from '@mui/icons-material/PersonSearch';

// icons
const icons = {
  JobRequest: PersonSearchIcon
};

// ==============================|| MENU ITEMS - SAMPLE PAGE ||============================== //

const jobrequest = {
  id: 'jobrequest',
  title: <FormattedMessage id="Job Posting" />,
  type: 'group',
  url: '/jobrequest',
  icon: icons.JobRequest
};

export default jobrequest;
