// This is example of menu item without group for horizontal layout. There will be no children.

// third-party
import { FormattedMessage } from 'react-intl';

// assets
import { DocumentCode2, Home } from 'iconsax-react';
import AdminPanelSettingsOutlinedIcon from '@mui/icons-material/AdminPanelSettingsOutlined';
import BusinessIcon from '@mui/icons-material/Business';
import SearchIcon from '@mui/icons-material/Search';
import TravelExploreIcon from '@mui/icons-material/TravelExplore';
import EventSeatIcon from '@mui/icons-material/EventSeat';
import GroupIcon from '@mui/icons-material/Group';
import PeopleIcon from '@mui/icons-material/People';

// icons
const icons = {
  AdminSetup: AdminPanelSettingsOutlinedIcon,
  Clients: BusinessIcon,
  ClientLookups: SearchIcon,
  Bench: EventSeatIcon,
  Leads: TravelExploreIcon,
  Security: AdminPanelSettingsOutlinedIcon,
  SecurityLookups: SearchIcon
};

// ==============================|| MENU ITEMS - SAMPLE PAGE ||============================== //

const AdminSetup_panel = {
  id: 'admin-setup',
  title: <FormattedMessage id="Admin Setup Panel" />,
  type: 'group',
  url: '/admin-setup-panel',
  icon: icons.AdminSetup,
  breadcrumbs: true,
  children: [
    {
      id: 'clients',
      title: <FormattedMessage id="Clients" />,
      type: 'collapse',
      icon: icons.Clients,
      breadcrumbs: false,
      children: [
        {
          id: 'client-lookups',
          title: <FormattedMessage id="Client Lookups" />,
          type: 'item',
          url: '/admin-setup-panel/client/lookups',
          icon: icons.ClientLookups,
          breadcrumbs: true
        },
        {
          id: 'client-contact-visbility',
          title: <FormattedMessage id="Client Contact Visbility" />,
          type: 'item',
          url: '/admin-setup-panel/client/contact-visibility',
          icon: icons.ClientLookups,
          breadcrumbs: true
        },
        {
          id: 'client-document-types',
          title: <FormattedMessage id="Client Document Types" />,
          type: 'item',
          url: '/admin-setup-panel/client/document-types',
          icon: icons.ClientLookups,
          breadcrumbs: true
        },
        {
          id: 'client-tab-order',
          title: <FormattedMessage id="Client Tab Order" />,
          type: 'item',
          url: '/admin-setup-panel/client/tab-order',
          icon: icons.ClientLookups,
          breadcrumbs: true
        },
        {
          id: 'client-settings',
          title: <FormattedMessage id="Client Settings" />,
          type: 'item',
          url: '/admin-setup-panel/client/settings',
          icon: icons.ClientLookups,
          breadcrumbs: true
        }
      ]
    },
    {
      id: 'leads',
      title: <FormattedMessage id="leads" />,
      type: 'collapse',
      icon: icons.Leads,
      breadcrumbs: false,
      children: [
        {
          id: 'call-type',
          title: <FormattedMessage id="Leads Call Type" />,
          type: 'item',
          url: '/admin-setup-panel/leads/call-type',
          icon: icons.Leads,
          breadcrumbs: true
        },
        {
          id: 'settings',
          title: <FormattedMessage id="Leads Settings" />,
          type: 'item',
          url: '/admin-setup-panel/leads/settings',
          icon: icons.Leads,
          breadcrumbs: true
        },
        {
          id: 'sources',
          title: <FormattedMessage id="Leads Sources" />,
          type: 'item',
          url: '/admin-setup-panel/leads/sources',
          icon: icons.Leads,
          breadcrumbs: true
        },
        {
          id: 'statuses',
          title: <FormattedMessage id="Leads Statuses" />,
          type: 'item',
          url: '/admin-setup-panel/leads/statuses',
          icon: icons.Leads,
          breadcrumbs: true
        }
      ]
    },
    {
      id: 'Jobs',
      title: <FormattedMessage id="Jobs" />,
      type: 'collapse',
      icon: icons.Clients,
      breadcrumbs: false,
      children: [
        {
          id: 'company-overview',
          title: <FormattedMessage id="Company Overview" />,
          type: 'item',
          url: '/admin-setup-panel/jobs/component-overview',
          icon: icons.ClientLookups,
          breadcrumbs: true
        },
        {
          id: 'disqualification-reason',
          title: <FormattedMessage id="Disqualification Reason" />,
          type: 'item',
          url: '/admin-setup-panel/jobs/disqualification-reason',
          icon: icons.ClientLookups,
          breadcrumbs: true
        },
        {
          id: 'interview-settings',
          title: <FormattedMessage id="Interview Settings" />,
          type: 'item',
          url: '/admin-setup-panel/jobs/interview-setting',
          icon: icons.ClientLookups,
          breadcrumbs: true
        },
        {
          id: 'job-posting-settings',
          title: <FormattedMessage id="Job Posting Settings" />,
          type: 'item',
          url: '/admin-setup-panel/jobs/jobposting-setting',
          icon: icons.ClientLookups,
          breadcrumbs: true
        },
        {
          id: 'job-posting-lookups',
          title: <FormattedMessage id="Job Posting Lookups" />,
          type: 'item',
          url: '/admin-setup-panel/jobs/job-posting-lookups',
          icon: icons.ClientLookups,
          breadcrumbs: true
        },
        {
          id: 'job-posting-statuses',
          title: <FormattedMessage id="Job Posting Statuses" />,
          type: 'item',
          url: '/admin-setup-panel/jobs/job-posting-statuses',
          icon: icons.ClientLookups,
          breadcrumbs: true
        },
        {
          id: 'job-posting-summary',
          title: <FormattedMessage id="Job Posting Summary" />,
          type: 'item',
          url: '/admin-setup-panel/jobs/job-posting-summary',
          icon: icons.ClientLookups,
          breadcrumbs: true
        },
        {
          id: 'key-work-masking',
          title: <FormattedMessage id="Key Work Masking" />,
          type: 'item',
          url: '/admin-setup-panel/jobs/key-work-masking',
          icon: icons.ClientLookups,
          breadcrumbs: true
        },
        {
          id: 'priorities',
          title: <FormattedMessage id="Priorities" />,
          type: 'item',
          url: '/admin-setup-panel/jobs/priorities',
          icon: icons.ClientLookups,
          breadcrumbs: true
        }
      ]
    },
    {
      id: ' bench',
      title: <FormattedMessage id="bench" />,
      type: 'collapse',
      icon: icons.Bench,
      breadcrumbs: false,
      children: [
        {
          id: 'bench-age-configuration',
          title: <FormattedMessage id="Bench Age Configuration" />,
          type: 'item',
          url: '/admin-setup-panel/bench/bench-age-configuration',
          icon: icons.Bench,
          breadcrumbs: true
        },
        {
          id: 'talent-bench-lookups',
          title: <FormattedMessage id="Talent Bench Lookups" />,
          type: 'item',
          url: '/admin-setup-panel/bench/talent-bench-lookups',
          icon: icons.Bench,
          breadcrumbs: true
        },
        {
          id: 'talent-bench-settings',
          title: <FormattedMessage id="Talent Bench Settings" />,
          type: 'item',
          url: '/admin-setup-panel/bench/talent-bench-settings',
          icon: icons.Bench,
          breadcrumbs: true
        },
        {
          id: 'talent-bench-tab-order',
          title: <FormattedMessage id="Talent Bench Tab Order" />,
          type: 'item',
          url: '/admin-setup-panel/bench/talent-bench-tab-order',
          icon: icons.Bench,
          breadcrumbs: true
        }
      ]
    },
    {
      id: 'vendors',
      title: <FormattedMessage id="vendors" />,
      type: 'collapse',
      icon: icons.Leads,
      breadcrumbs: false,
      children: [
        {
          id: 'vendor-contact-statuses',
          title: <FormattedMessage id="Vendor Contact Statuses" />,
          type: 'item',
          url: '/admin-setup-panel/vendors/vendor-contact-statuses',
          icon: icons.Leads,
          breadcrumbs: true
        },
        {
          id: 'vendor-document-types',
          title: <FormattedMessage id="Vendor Document types" />,
          type: 'item',
          url: '/admin-setup-panel/vendors/vendor/vendor-document-types',
          icon: icons.Leads,
          breadcrumbs: true
        },
        {
          id: 'vendor-settings',
          title: <FormattedMessage id="Vendor Settings" />,
          type: 'item',
          url: '/admin-setup-panel/vendors/vendor-settings',
          icon: icons.Leads,
          breadcrumbs: true
        }
      ]
    },
    {
      id: 'placements',
      title: <FormattedMessage id="placements" />,
      type: 'collapse',
      icon: icons.Clients,
      breadcrumbs: false,
      children: [
        {
          id: 'cost-sheet',
          title: <FormattedMessage id="Cost Sheet" />,
          type: 'item',
          url: '/admin-setup-panel/placement',
          icon: icons.ClientLookups,
          breadcrumbs: true
          // children:[
          //   {
          //     id: 'cost-sheet-details',
          //     title: <FormattedMessage id="Cost Sheet Details" />,
          //     type: 'item',
          //     url: '/admin-setup-panel/placements/cost-sheet/:id',
          //     icon: icons.ClientLookups,
          //     breadcrumbs: true,
          //   }
          // ]
        },
        {
          id: 'over-head',
          title: <FormattedMessage id="Overhead / Expenses" />,
          type: 'item',
          url: '/admin-setup-panel/placements/over-head',
          icon: icons.ClientLookups,
          breadcrumbs: true
        },
        {
          id: 'net-margin-formula',
          title: <FormattedMessage id="Net Margin Formula For India Placements" />,
          type: 'item',
          url: '/admin-setup-panel/placements/net-margin-formula',
          icon: icons.ClientLookups,
          breadcrumbs: true
        },
        {
          id: 'pay-breakup-settings',
          title: <FormattedMessage id="Pay Breakup Settings" />,
          type: 'item',
          url: '/admin-setup-panel/placements/pay-breakup-settings',
          icon: icons.ClientLookups,
          breadcrumbs: true
        },
        {
          id: 'placement-lookups',
          title: <FormattedMessage id="Placement Lookups" />,
          type: 'item',
          url: '/admin-setup-panel/placements/placement-lookups',
          icon: icons.ClientLookups,
          breadcrumbs: true
        },
        {
          id: 'placement-settings',
          title: <FormattedMessage id="Placement Settings" />,
          type: 'item',
          url: '/admin-setup-panel/placements/placement-settings',
          icon: icons.ClientLookups,
          breadcrumbs: true
        },
        {
          id: 'profile-margin-settings',
          title: <FormattedMessage id="Profile Margin Settings" />,
          type: 'item',
          url: '/admin-setup-panel/placements/profile-margin-settings',
          icon: icons.ClientLookups,
          breadcrumbs: true
        }
      ]
    },
    {
      id: 'campus',
      title: <FormattedMessage id="Campus" />,
      type: 'collapse',
      icon: icons.Clients,
      breadcrumbs: false,
      children: [
        {
          id: 'campus',
          title: <FormattedMessage id="Campus Lookups" />,
          type: 'item',
          url: '/admin-setup-panel/campus/lookups',
          icon: icons.ClientLookups,
          breadcrumbs: true
        }
      ]
    },
    {
      id: 'placements',
      title: <FormattedMessage id="placements" />,
      type: 'collapse',
      icon: icons.Clients,
      breadcrumbs: false,
      children: [
        {
          id: 'cost-sheet',
          title: <FormattedMessage id="Cost Sheet" />,
          type: 'item',
          url: '/admin-setup-panel/placement',
          icon: icons.ClientLookups,
          breadcrumbs: true
          // children:[
          //   {
          //     id: 'cost-sheet-details',
          //     title: <FormattedMessage id="Cost Sheet Details" />,
          //     type: 'item',
          //     url: '/admin-setup-panel/placements/cost-sheet/:id',
          //     icon: icons.ClientLookups,
          //     breadcrumbs: true,
          //   }
          // ]
        },
        {
          id: 'over-head',
          title: <FormattedMessage id="Overhead / Expenses" />,
          type: 'item',
          url: '/admin-setup-panel/placements/over-head',
          icon: icons.ClientLookups,
          breadcrumbs: true
        },
        {
          id: 'net-margin-formula',
          title: <FormattedMessage id="Net Margin Formula For India Placements" />,
          type: 'item',
          url: '/admin-setup-panel/placements/net-margin-formula',
          icon: icons.ClientLookups,
          breadcrumbs: true
        },
        {
          id: 'pay-breakup-settings',
          title: <FormattedMessage id="Pay Breakup Settings" />,
          type: 'item',
          url: '/admin-setup-panel/placements/pay-breakup-settings',
          icon: icons.ClientLookups,
          breadcrumbs: true
        },
        {
          id: 'placement-lookups',
          title: <FormattedMessage id="Placement Lookups" />,
          type: 'item',
          url: '/admin-setup-panel/placements/placement-lookups',
          icon: icons.ClientLookups,
          breadcrumbs: true
        },
        {
          id: 'placement-settings',
          title: <FormattedMessage id="Placement Settings" />,
          type: 'item',
          url: '/admin-setup-panel/placements/placement-settings',
          icon: icons.ClientLookups,
          breadcrumbs: true
        },
        {
          id: 'profile-margin-settings',
          title: <FormattedMessage id="Profile Margin Settings" />,
          type: 'item',
          url: '/admin-setup-panel/placements/profile-margin-settings',
          icon: icons.ClientLookups,
          breadcrumbs: true
        }
      ]
    },
    {
      id: 'security',
      title: <FormattedMessage id="Security" />,
      type: 'collapse',
      icon: icons.Security,
      breadcrumbs: false,
      children: [
        {
          id: 'access-control',
          title: <FormattedMessage id="Access Control" />,
          type: 'item',
          url: '/admin-setup-panel/security/access-control',
          icon: icons.SecurityLookups,
          breadcrumbs: true
        }
      ]
    },
    {
      id: 'applicant',
      title: <FormattedMessage id="applicant" />,

      type: 'collapse',

      icon: icons.Clients,

      breadcrumbs: false,

      children: [
        {
          id: 'applicant-lookups',

          title: <FormattedMessage id="Applicant Lookups" />,

          type: 'item',

          url: '/admin-setup-panel/applicants/applicant-lookups',

          icon: icons.ClientLookups,

          breadcrumbs: true
        },

        {
          id: 'applicant-settings',

          title: <FormattedMessage id="Applicant Settings" />,

          type: 'item',

          url: '/admin-setup-panel/applicants/applicant-settings',

          icon: icons.ClientLookups,

          breadcrumbs: true
        },

        {
          id: 'applicant-sources',

          title: <FormattedMessage id="Applicant Sources" />,

          type: 'item',

          url: '/admin-setup-panel/applicants/applicant-sources',

          icon: icons.ClientLookups,

          breadcrumbs: true
        },

        {
          id: 'applicant-statuses',

          title: <FormattedMessage id="Applicant Statuses" />,

          type: 'item',

          url: '/admin-setup-panel/applicants/applicant-statuses',

          icon: icons.ClientLookups,

          breadcrumbs: true
        },

        {
          id: 'applicant-tab',

          title: <FormattedMessage id="Applicant Tab Order" />,

          type: 'item',

          url: '/admin-setup-panel/applicants/applicant-order',

          icon: icons.ClientLookups,

          breadcrumbs: true
        },

        {
          id: 'applicant-configuration',

          title: <FormattedMessage id="Applicant Configuration" />,

          type: 'item',

          url: '/admin-setup-panel/applicants/applicant-configuration',

          icon: icons.ClientLookups,

          breadcrumbs: true
        },

        {
          id: 'notice-periods',

          title: <FormattedMessage id="Notice Periods" />,

          type: 'item'
        },
        {
          id: 'security',
          title: <FormattedMessage id="Security" />,
          type: 'collapse',
          icon: icons.Security,
          breadcrumbs: false,
          children: [
            {
              id: 'access-control',
              title: <FormattedMessage id="Access Control" />,
              type: 'item',
              url: '/admin-setup-panel/security/access-control',
              icon: icons.SecurityLookups,
              breadcrumbs: true
            },
            {
              id: 'access-settings',
              title: <FormattedMessage id="Access Settings" />,
              type: 'item',
              url: '/admin-setup-panel/security/access-control/access-settings',
              icon: icons.SecurityLookups,
              breadcrumbs: true
            },
            {
              id: 'bi-report-access-control',
              title: <FormattedMessage id="BI Report Access Control" />,
              type: 'item',
              url: '/admin-setup-panel/security/BI-report-access-control',
              icon: icons.SecurityLookups,
              breadcrumbs: true
            },
            {
              id: 'eboarding-settings',
              title: <FormattedMessage id="Eboarding Settings" />,
              type: 'item',
              url: '/admin-setup-panel/security/eboarding-settings',
              icon: icons.SecurityLookups,
              breadcrumbs: true
            },
            {
              id: 'import-export-history',
              title: <FormattedMessage id="Import/Export History" />,
              type: 'item',
              url: '/admin-setup-panel/security/import-export-history',
              icon: icons.SecurityLookups,
              breadcrumbs: true
            },
            {
              id: 'ip-configuration',
              title: <FormattedMessage id="IP Configuration" />,
              type: 'item',
              url: '/admin-setup-panel/security/ipconfiguration',
              icon: icons.SecurityLookups,
              breadcrumbs: true
            },
            {
              id: 'jobseeker',
              title: <FormattedMessage id="Jobseekers" />,
              type: 'item',
              url: '/admin-setup-panel/security/jobseeker',
              icon: icons.SecurityLookups,
              breadcrumbs: true
            },
            {
              id: 'password-configuration',
              title: <FormattedMessage id="Password Configuration" />,
              type: 'item',
              url: '/admin-setup-panel/security/password-configuration',
              icon: icons.SecurityLookups,
              breadcrumbs: true
            },
            {
              id: 'quick-apps-access-config',
              title: <FormattedMessage id="Quick Apps Access Config" />,
              type: 'item',
              url: '/admin-setup-panel/security/quick-apps-access-config',
              icon: icons.SecurityLookups,
              breadcrumbs: true
            },
            {
              id: 'roles',
              title: <FormattedMessage id="Roles" />,
              type: 'item',
              url: '/admin-setup-panel/security/roles',
              icon: GroupIcon,
              breadcrumbs: true
            },
            {
              id: 'teams',
              title: <FormattedMessage id="Teams" />,
              type: 'item',
              url: '/admin-setup-panel/security/teams',
              icon: GroupIcon,
              breadcrumbs: true
            },
            {
              id: 'teams',
              title: <FormattedMessage id="Teams" />,
              type: 'item',
              url: '/admin-setup-panel/security/teams/edit',
              icon: GroupIcon,
              breadcrumbs: true
            },
            {
              id: 'teams',
              title: <FormattedMessage id="Teams" />,
              type: 'item',
              url: '/admin-setup-panel/security/teams/add',
              icon: GroupIcon,
              breadcrumbs: true
            },
            {
              id: 'user-field-restriction',
              title: <FormattedMessage id="User Field Restriction" />,
              type: 'item',
              url: '/admin-setup-panel/security/user-field-restriction',
              icon: GroupIcon,
              breadcrumbs: true
            },
            {
              id: 'users',
              title: <FormattedMessage id="Users" />,
              type: 'item',
              url: '/admin-setup-panel/security/user',
              icon: PeopleIcon,
              breadcrumbs: true
            }
          ]
        },

        {
          id: 'applicant-notice-period',
          title: <FormattedMessage id="Applicant Notice Period" />,
          type: 'item',
          url: '/admin-setup-panel/applicants/applicant-notice-period',

          icon: icons.ClientLookups,

          breadcrumbs: true
        },

        {
          id: 'application-statuses',

          title: <FormattedMessage id="Application Statuses" />,

          type: 'item',

          url: '/admin-setup-panel/applicants/application-statuses',

          icon: icons.ClientLookups,

          breadcrumbs: true
        },
        {
          id: 'application-statuses',

          title: <FormattedMessage id="Application Statuses" />,

          type: 'item',

          url: '/admin-setup-panel/applicants/application-statuses/add',

          icon: icons.ClientLookups,

          breadcrumbs: true
        },
        {
          id: 'application-statuses',

          title: <FormattedMessage id="Application Statuses" />,

          type: 'item',

          url: '/admin-setup-panel/applicants/application-statuses/edit',

          icon: icons.ClientLookups,

          breadcrumbs: true
        },

        {
          id: 'career-sites',

          title: <FormattedMessage id="Career Sites" />,

          type: 'item',

          url: '/admin-setup-panel/applicants/career-sites',

          icon: icons.ClientLookups,

          breadcrumbs: true
        },
        {
          id: 'career-sites',

          title: <FormattedMessage id="Career Sites" />,

          type: 'item',

          url: '/admin-setup-panel/applicants/career-sites/add',

          icon: icons.ClientLookups,

          breadcrumbs: true
        },
        {
          id: 'career-sites',

          title: <FormattedMessage id="Career Sites" />,

          type: 'item',

          url: '/admin-setup-panel/applicants/career-sites/edit',

          icon: icons.ClientLookups,

          breadcrumbs: true
        },


        {
          id: 'custom-applicants',

          title: <FormattedMessage id="Custom Applicants" />,

          type: 'item',

          url: '/admin-setup-panel/applicants/custom-applicants',

          icon: icons.ClientLookups,

          breadcrumbs: true
        },

        {
          id: 'document-types',

          title: <FormattedMessage id="Document Type" />,

          type: 'item',

          url: '/admin-setup-panel/applicants/document-types',

          icon: icons.ClientLookups,

          breadcrumbs: true
        },

        {
          id: 'pipeline-statuses',

          title: <FormattedMessage id="Pipeline Statuses" />,

          type: 'item',

          url: '/admin-setup-panel/applicants/pipeline-statuses',

          icon: icons.ClientLookups,

          breadcrumbs: true
        },

        {
          id: 'profile-download',

          title: <FormattedMessage id="Profile Download" />,

          type: 'item',

          url: '/admin-setup-panel/applicants/profile-download',

          icon: icons.ClientLookups,

          breadcrumbs: true
        },

        {
          id: 'resume-builder',

          title: <FormattedMessage id="Resume Builder" />,

          type: 'item',

          url: '/admin-setup-panel/applicants/resume-builder',

          icon: icons.ClientLookups,

          breadcrumbs: true
        },

        {
          id: 'submissions',

          title: <FormattedMessage id="Submissions" />,

          type: 'item',

          url: '/admin-setup-panel/applicants/submissions',

          icon: icons.ClientLookups,

          breadcrumbs: true
        }
      ]
    },

    {
      id: 'reminder',
      title: <FormattedMessage id="Reminder" />,
      type: 'collapse',
      icon: EventSeatIcon,
      breadcrumbs: false,
      children: [
        {
          id: 'applicantprofile-reminder',
          title: <FormattedMessage id="Applicant Profile Reminder" />,
          type: 'item',
          url: '/admin-setup-panel/reminder/applicantprofile',
          icon: EventSeatIcon,
          breadcrumbs: true
        },
        {
          id: 'client-reminder',
          title: <FormattedMessage id="Client Reminder" />,
          type: 'item',
          url: '/admin-setup-panel/reminder/client',
          icon: EventSeatIcon,
          breadcrumbs: true
        },
        {
          id: 'email-statistics-reminder',
          title: <FormattedMessage id="Email Statistics" />,
          type: 'item',
          url: '/admin-setup-panel/reminder/email-statistics',
          icon: EventSeatIcon,
          breadcrumbs: true
        },
        {
          id: 'general-reminder',
          title: <FormattedMessage id="General" />,
          type: 'item',
          url: '/admin-setup-panel/reminder/general',
          icon: EventSeatIcon,
          breadcrumbs: true
        },
        {
          id: 'job-posting-reminder',
          title: <FormattedMessage id="Job Posting" />,
          type: 'item',
          url: '/admin-setup-panel/reminder/job-posting',
          icon: EventSeatIcon,
          breadcrumbs: true
        },
        {
          id: 'placements-reminder',
          title: <FormattedMessage id="Placements" />,
          type: 'item',
          url: '/admin-setup-panel/reminder/placements',
          icon: EventSeatIcon,
          breadcrumbs: true
        },
        {
          id: 'talent-bench-reminder',
          title: <FormattedMessage id="Talent Bench" />,
          type: 'item',
          url: '/admin-setup-panel/reminder/talent-bench',
          icon: EventSeatIcon,
          breadcrumbs: true
        },
        {
          id: 'vendor-reminder',
          title: <FormattedMessage id="Vendors" />,
          type: 'item',
          url: '/admin-setup-panel/reminder/vendors',
          icon: EventSeatIcon,
          breadcrumbs: true
        }
      ]
    },

    {
      id: 'integration',
      title: <FormattedMessage id="Integration" />,
      type: 'collapse',
      icon: icons.Security,
      breadcrumbs: false,
      children: [
        {
          id: 'access-control',
          title: <FormattedMessage id="All Integration" />,
          type: 'item',
          url: '/admin-setup-panel/integration/all',
          icon: icons.SecurityLookups,
          breadcrumbs: true
        },
        {
          id: 'resume-search-account',
          title: <FormattedMessage id="Resume Search Account" />,
          type: 'item',
          url: '/admin-setup-panel/integration/resume-search-account',
          icon: icons.SecurityLookups,
          breadcrumbs: true
        },
        {
          id: 'job-posting-account',
          title: <FormattedMessage id="Job Posting Account" />,
          type: 'item',
          url: '/admin-setup-panel/integration/job-posting-account',
          icon: icons.SecurityLookups,
          breadcrumbs: true
        },
        {
          id: 'email-integration',
          title: <FormattedMessage id="Email Integration" />,
          type: 'item',
          url: '/admin-setup-panel/integration/email-integration',
          icon: icons.SecurityLookups,
          breadcrumbs: true
        },
        {
          id: 'integration-settings',
          title: <FormattedMessage id="Integration Settings" />,
          type: 'item',
          url: '/admin-setup-panel/integration/integration-settings',
          icon: icons.SecurityLookups
        },
        {
          id: 'market-place',
          title: <FormattedMessage id="Market Place" />,
          type: 'item',
          url: '/admin-setup-panel/integration/market-place',
          icon: icons.SecurityLookups
        },
        {
          id: 'vsm-integration',
          title: <FormattedMessage id="VSM Accounts" />,
          type: 'item',
          url: '/admin-setup-panel/integration/vsm-integration',
          icon: icons.SecurityLookups
        },
        {
          id: 'vsm-job-auto-publish',
          title: <FormattedMessage id="VSM Job Auto Publish" />,
          type: 'item',
          url: '/admin-setup-panel/integration/vsm-job-auto-publish',
          icon: icons.SecurityLookups
        },
        {
          id: 'vsm-job-questions',
          title: <FormattedMessage id="VSM Job Questions" />,
          type: 'item',
          url: '/admin-setup-panel/integration/vsm-job-questions',
          icon: icons.SecurityLookups
        },
        {
          id: 'passive-applicants',
          title: <FormattedMessage id="Passive Applicants" />,
          type: 'item',
          url: '/admin-setup-panel/integration/passive-applicants',
          icon: icons.SecurityLookups
        }
      ]
    },
    {
      id: 'global-settings',
      title: <FormattedMessage id="Global Settings" />,
      type: 'collapse',
      icon: icons.Security,
      breadcrumbs: false,
      children: [
        {
          id: 'custom-fields',
          title: <FormattedMessage id="Custom Fields" />,
          type: 'item',
          url: '/admin-setup-panel/global-settings/custom-fields',
          icon: icons.SecurityLookups,
          breadcrumbs: true
        },
        {
          id: 'email-settings',
          title: <FormattedMessage id="Email Settings" />,
          type: 'item',
          url: '/admin-setup-panel/global-settings/email-settings',
          icon: icons.SecurityLookups,
          breadcrumbs: true
        },
        {
          id: 'concent-settings',
          title: <FormattedMessage id="Concent Settings" />,
          type: 'item',
          url: '/admin-setup-panel/global-settings/concent-settings',
          icon: icons.SecurityLookups,
          breadcrumbs: true
        },
        {
          id: 'lookups',
          title: <FormattedMessage id="Lookups" />,
          type: 'item',
          url: '/admin-setup-panel/global-settings/lookups',
          icon: icons.SecurityLookups,
          breadcrumbs: true
        },
        {
          id: 'mandatory-fields',
          title: <FormattedMessage id="Mandatory Fields" />,
          type: 'item',
          url: '/admin-setup-panel/global-settings/mandatory-fields',
          icon: icons.SecurityLookups,
          breadcrumbs: true
        }
      ]
    },
    {
      id: 'organization',
      title: <FormattedMessage id="organization" />,
      type: 'collapse',
      icon: icons.Clients,
      breadcrumbs: false,
      children: [
        {
          id: 'billing-details',
          title: <FormattedMessage id="Billing Details" />,
          type: 'item',
          url: '/admin-setup-panel/organization/billing-details',
          icon: icons.Clients,
          breadcrumbs: true
        },
        {
          id: 'business-units',
          title: <FormattedMessage id="Business Units" />,
          type: 'item',
          url: '/admin-setup-panel/organization/business-units',
          icon: icons.Clients,
          breadcrumbs: true
        },
        {
          id: 'business-units-notifications',
          title: <FormattedMessage id="Business Units - Notifications Config" />,
          type: 'item',
          url: '/admin-setup-panel/organization/business-units/notifications-config',
          icon: icons.Clients,
          breadcrumbs: true
        },
        {
          id: 'business-units-edit',
          title: <FormattedMessage id="Business Units" />,
          type: 'item',
          url: '/admin-setup-panel/organization/business-units/edit',
          icon: icons.Clients,
          breadcrumbs: true
        },
        {
          id: 'business-units',
          title: <FormattedMessage id="Business Units - Activities" />,
          type: 'item',
          url: '/admin-setup-panel/organization/business-units/activities',
          icon: icons.Clients,
          breadcrumbs: true
        },
        {
          id: 'business-units',
          title: <FormattedMessage id="Business Units - Custom Labels" />,
          type: 'item',
          url: '/admin-setup-panel/organization/business-units/custom-labels',
          icon: icons.Clients,
          breadcrumbs: true
        },
        {
          id: 'custom-email-templates',
          title: <FormattedMessage id="Custom Email Templates" />,
          type: 'item',
          url: '/admin-setup-panel/organization/custom-email-templates',
          icon: icons.Clients,
          breadcrumbs: true
        },
        {
          id: 'email-templates',
          title: <FormattedMessage id="Email Templates" />,
          type: 'item',
          url: '/admin-setup-panel/organization/email-templates',
          icon: icons.Clients,
          breadcrumbs: true
        },
        {
          id: 'hierarchy',
          title: <FormattedMessage id="Hierarchy" />,
          type: 'item',
          url: '/admin-setup-panel/organization/hierarchy',
          icon: icons.Clients,
          breadcrumbs: true
        },
        {
          id: 'list-view-sorting',
          title: <FormattedMessage id="List View Sorting" />,
          type: 'item',
          url: '/admin-setup-panel/organization/list-view-sorting',
          icon: icons.Clients,
          breadcrumbs: true
        },
        {
          id: 'organization-lookups',
          title: <FormattedMessage id="Organization Lookups" />,
          type: 'item',
          url: '/admin-setup-panel/organization/organization-lookups',
          icon: icons.Clients,
          breadcrumbs: true
        },
        {
          id: 'settings',
          title: <FormattedMessage id="Settings" />,
          type: 'item',
          url: '/admin-setup-panel/organization/settings',
          icon: icons.Clients,
          breadcrumbs: true
        },
        {
          id: 'ssl-client-certificate',
          title: <FormattedMessage id="SSL Client Certificate" />,
          type: 'item',
          url: '/admin-setup-panel/organization/ssl-client-certificate',
          icon: icons.Clients,
          breadcrumbs: true
        },
        {
          id: 'submission-tabs-display',
          title: <FormattedMessage id="Submission Tabs Display" />,
          type: 'item',
          url: '/admin-setup-panel/organization/submission-tabs-display',
          icon: icons.Clients,
          breadcrumbs: true
        },
        {
          id: 'data-backup',
          title: <FormattedMessage id="Data Backup" />,
          type: 'item',
          url: '/admin-setup-panel/organization/data-backup',
          icon: icons.Clients,
          breadcrumbs: true
        },
        {
          id: 'target-settings',
          title: <FormattedMessage id="Target Settings" />,
          type: 'item',
          url: '/admin-setup-panel/organization/target-settings',
          icon: icons.Clients,
          breadcrumbs: true
        }
      ]
    },
    {
      id: 'requisition',
      title: <FormattedMessage id="Requisition" />,
      type: 'collapse',
      icon: icons.Clients,
      breadcrumbs: false,
      children: [
        {
          id: 'job-requisition-code',
          title: <FormattedMessage id="Job Requisition Code" />,
          type: 'item',
          url: '/admin-setup-panel/requisition/job-requisition-code',
          icon: icons.Clients,
          breadcrumbs: true
        },
        {
          id: 'job-requisition-lookups',
          title: <FormattedMessage id="Job Requisition Lookups" />,
          type: 'item',
          url: '/admin-setup-panel/requisition/job-requisition-lookups',
          icon: icons.Clients,
          breadcrumbs: true
        },
        {
          id: 'job-requisition-settings',
          title: <FormattedMessage id="Job Requisition Settings" />,
          type: 'item',
          url: '/admin-setup-panel/requisition/job-requisition-settings',
          icon: icons.Clients,
          breadcrumbs: true
        },
        {
          id: 'job-requisition-workflow',
          title: <FormattedMessage id="Job Requisition Workflow" />,
          type: 'item',
          url: '/admin-setup-panel/requisition/job-requisition-workflow',
          icon: icons.Clients,
          breadcrumbs: true
        }
      ]
    }
  ]
};

export default AdminSetup_panel;
