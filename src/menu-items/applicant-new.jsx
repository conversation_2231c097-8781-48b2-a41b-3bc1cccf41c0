// This is example of menu item without group for horizontal layout. There will be no children.

// third-party
import { FormattedMessage } from 'react-intl';

// assets
import { DocumentCode2, Home} from 'iconsax-react';
import EngineeringIcon from '@mui/icons-material/Engineering';
// type

// icons
const icons = {
  applyjob: EngineeringIcon
};

// ==============================|| MENU ITEMS - SAMPLE PAGE ||============================== //

const applicant = {
  id: 'group-inputs',
  title: <FormattedMessage id="Applicants" />,
  type: 'group',
  url: '/applicant',
  icon: icons.applyjob
};

export default applicant;
