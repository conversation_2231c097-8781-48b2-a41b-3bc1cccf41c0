// This is example of menu item without group for horizontal layout. There will be no children.

// third-party
import { FormattedMessage } from 'react-intl';

// assets
import { DocumentCode2, Graph, People} from 'iconsax-react';
import TravelExploreIcon from '@mui/icons-material/TravelExplore';
// type

// icons
const icons = {
  samplePage: TravelExploreIcon
};

// ==============================|| MENU ITEMS - SAMPLE PAGE ||============================== //

const leads = {
  id: 'leads-group',
  title: <FormattedMessage id="Leads" />,
  type: 'group',
  url: '/leads',
  icon: icons.samplePage
};

export default leads;
