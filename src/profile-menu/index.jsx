// import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'; // Use Routes from react-router-dom
// import ContactInfo from 'pages/profile'; // Assuming ContactInfo is your profile page component

// const App = () => {
//     console.log('Rendering App component'); // Check if App is rendered
//     return (
//       <Router>
//         <Routes>
//           <Route
//             path="/view-profile"
//             element={<ContactInfo />}
//           />
//         </Routes>
//       </Router>
//     );
//   };
  
//   export default App;


const App = () => {
  return <h1>Hello World</h1>;
};

export default App;
