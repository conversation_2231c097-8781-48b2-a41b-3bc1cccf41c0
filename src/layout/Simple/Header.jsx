import PropTypes from 'prop-types';
import { useState, cloneElement } from 'react';
import { Link as RouterLink } from 'react-router-dom';

// material-ui
import { alpha, useTheme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import AppBar from '@mui/material/AppBar';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Container from '@mui/material/Container';
import Drawer from '@mui/material/Drawer';
import Link from '@mui/material/Link';
import List from '@mui/material/List';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import Stack from '@mui/material/Stack';
import Toolbar from '@mui/material/Toolbar';
import Typography from '@mui/material/Typography';
import useScrollTrigger from '@mui/material/useScrollTrigger';
import InputAdornment from '@mui/material/InputAdornment';
import TextField from '@mui/material/TextField';

// project-imports
import { APP_DEFAULT_PATH, ThemeDirection } from 'config';
import IconButton from 'components/@extended/IconButton';
import Logo from 'components/logo';
import Avatar from 'components/@extended/Avatar';
import { handlerComponentDrawer, useGetMenuMaster } from 'api/menu';

// assets
import { HambergerMenu, Minus, SearchNormal1, User } from 'iconsax-react';

// elevation scroll
function ElevationScroll({ children, window }) {
  const theme = useTheme();
  const trigger = useScrollTrigger({
    disableHysteresis: true,
    threshold: 10,
    target: window ? window : undefined
  });

  return cloneElement(children, {
    style: {
      boxShadow: trigger ? '0 8px 6px -10px rgba(0, 0, 0, 0.5)' : 'none',
      backgroundColor: trigger ? alpha(theme.palette.background.default, 0.8) : alpha(theme.palette.background.default, 0.1)
    }
  });
}

// ==============================|| COMPONENTS - APP BAR ||============================== //

export default function Header({ layout = 'landing', ...others }) {
  const theme = useTheme();
  const matchDownMd = useMediaQuery(theme.breakpoints.down('md'));
  const [drawerToggle, setDrawerToggle] = useState(false);
  const [searchValue, setSearchValue] = useState('');

  const { menuMaster } = useGetMenuMaster();

  const drawerToggler = (open) => (event) => {
    if (event.type === 'keydown' && (event.key === 'Tab' || event.key === 'Shift')) {
      return;
    }
    setDrawerToggle(open);
  };

  return (
    <ElevationScroll layout={layout} {...others}>
      <AppBar
        sx={{
          bgcolor: alpha(theme.palette.background.default, 0.1),
          backdropFilter: 'blur(8px)',
          color: theme.palette.text.primary,
          boxShadow: 'none'
        }}
      >
        <Container maxWidth="xl" disableGutters={matchDownMd}>
          <Toolbar sx={{ px: { xs: 1.5, sm: 4, md: 0, lg: 0 }, py: 1 }}>
            <Stack direction="row" sx={{ flexGrow: 1, display: { xs: 'none', md: 'block' } }} alignItems="center">
              <Typography sx={{ textAlign: 'left', display: 'inline-block' }}>
                <Logo to="/" />
              </Typography>
            </Stack>

            {/* Search Bar - Visible on md and up */}
            <Box sx={{ display: { xs: 'none', md: 'block' }, mr: 2, flexGrow: 0.5 }}>
              <TextField
                variant="outlined"
                size="small"
                placeholder="Search..."
                value={searchValue}
                onChange={(e) => setSearchValue(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchNormal1 size="18" color={theme.palette.text.secondary} />
                    </InputAdornment>
                  ),
                  sx: {
                    borderRadius: '8px',
                    backgroundColor: alpha(theme.palette.background.paper, 0.5),
                    '&:hover': {
                      backgroundColor: alpha(theme.palette.background.paper, 0.7),
                    },
                    '&.Mui-focused': {
                      backgroundColor: alpha(theme.palette.background.paper, 0.7),
                      boxShadow: `0 0 0 2px ${theme.palette.primary.main}`
                    }
                  }
                }}
                sx={{ width: '100%', maxWidth: '400px' }}
              />
            </Box>

            <Stack
              direction="row"
              spacing={3}
              sx={{
                '& .header-link': { fontWeight: 500, '&:hover': { color: 'primary.main' } },
                display: { xs: 'none', md: 'block' }
              }}
              alignItems="center"
            >
              <Link
                className="header-link"
                color="secondary.main"
                component={RouterLink}
                to="/login"
                target="_blank"
                underline="none"
              >
                Dashboard
              </Link>

              {/* Profile Icon - Visible on md and up */}
              <IconButton
                color="secondary"
                sx={{
                  p: 0.5,
                  borderRadius: '50%',
                  '&:hover': { bgcolor: alpha(theme.palette.primary.lighter, 0.3) }
                }}
                // onClick={handleProfileMenuOpen} // Add your profile menu handler here
              >
                <Avatar alt="User Profile" size="sm">
                  <User size="22" color={theme.palette.text.secondary} />
                </Avatar>
              </IconButton>
            </Stack>

            <Box
              sx={{
                width: '100%',
                alignItems: 'center',
                justifyContent: 'space-between',
                display: { xs: 'flex', md: 'none' }
              }}
            >
              <Typography sx={{ textAlign: 'left', display: 'inline-block' }}>
                <Logo to="/" />
              </Typography>
              <Stack direction="row" spacing={2}>
                {/* Search Bar - Visible on xs to md */}
                <Box sx={{ display: { xs: 'block', md: 'none' }, flexGrow: 1, mr: 1 }}>
                  <TextField
                    variant="outlined"
                    size="small"
                    placeholder="Search..."
                    value={searchValue}
                    onChange={(e) => setSearchValue(e.target.value)}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <SearchNormal1 size="16" color={theme.palette.text.secondary} />
                        </InputAdornment>
                      ),
                      sx: {
                        borderRadius: '8px',
                        backgroundColor: alpha(theme.palette.background.paper, 0.5),
                        '&:hover': {
                          backgroundColor: alpha(theme.palette.background.paper, 0.7),
                        },
                        '&.Mui-focused': {
                          backgroundColor: alpha(theme.palette.background.paper, 0.7),
                        }
                      }
                    }}
                    sx={{ width: '100%' }}
                  />
                </Box>

                {layout === 'component' && (
                  <Button variant="outlined" component={RouterLink} to={APP_DEFAULT_PATH} sx={{ mt: 0.25 }}>
                    Dashboard
                  </Button>
                )}
                {layout !== 'component' && (
                  <Button variant="outlined" component={RouterLink} to="/login" sx={{ mt: 0.25 }}>
                    Dashboard
                  </Button>
                )}

                {/* Profile Icon - Visible on xs to md */}
                <IconButton
                  color="secondary"
                  sx={{
                    p: 0.5,
                    borderRadius: '50%',
                    '&:hover': { bgcolor: alpha(theme.palette.primary.lighter, 0.3) }
                  }}
                  // onClick={handleProfileMenuOpen} // Add your profile menu handler here
                >
                  <Avatar alt="User Profile" size="xs">
                    <User size="20" color={theme.palette.text.secondary} />
                  </Avatar>
                </IconButton>

                <IconButton
                  size="large"
                  color="secondary"
                  {...(layout === 'component'
                    ? { onClick: () => handlerComponentDrawer(!menuMaster.isComponentDrawerOpened) }
                    : { onClick: drawerToggler(true) })}
                  sx={{ p: 1 }}
                >
                  <HambergerMenu />
                </IconButton>
              </Stack>

              <Drawer
                anchor="top"
                open={drawerToggle}
                onClose={drawerToggler(false)}
                sx={{ '& .MuiDrawer-paper': { backgroundImage: 'none' } }}
              >
                <Box
                  sx={{
                    width: 'auto',
                    '& .MuiListItemIcon-root': {
                      fontSize: '1rem',
                      minWidth: 32
                    }
                  }}
                  role="presentation"
                  onClick={drawerToggler(false)}
                  onKeyDown={drawerToggler(false)}
                >
                  <List>
                    <Link style={{ textDecoration: 'none' }} href="/login" target="_blank">
                      <ListItemButton>
                        <ListItemIcon>
                          <Minus color={theme.palette.secondary.main} />
                        </ListItemIcon>
                        <ListItemText primary="Dashboard" primaryTypographyProps={{ variant: 'h6', color: 'secondary.main' }} />
                      </ListItemButton>
                    </Link>
                  </List>
                </Box>
              </Drawer>
            </Box>
          </Toolbar>
        </Container>
      </AppBar>
    </ElevationScroll>
  );
}

ElevationScroll.propTypes = { children: PropTypes.node, window: PropTypes.any };
Header.propTypes = { layout: PropTypes.string, others: PropTypes.any };
