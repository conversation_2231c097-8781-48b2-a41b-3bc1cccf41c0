import { useLayoutEffect, useState } from 'react';

import useMediaQuery from '@mui/material/useMediaQuery';
import Box from '@mui/material/Box';
import Divider from '@mui/material/Divider';
import List from '@mui/material/List';
import Typography from '@mui/material/Typography';

// project import
import NavItem from './NavItem';
import NavGroup from './NavGroup';
import menuItem from 'menu-items';

import useConfig from 'hooks/useConfig';
import { HORIZONTAL_MAX_ITEM, MenuOrientation } from 'config';
import { useGetMenuMaster } from 'api/menu';

// ==============================|| DRAWER CONTENT - NAVIGATION ||============================== //

export default function Navigation() {
  const { menuOrientation } = useConfig();
  const { menuMaster } = useGetMenuMaster();
  const drawerOpen = menuMaster.isDashboardDrawerOpened;
  const downLG = useMediaQuery((theme) => theme.breakpoints.down('lg'));

  const [selectedID, setSelectedID] = useState('');
  const [selectedItems, setSelectedItems] = useState('');
  const [selectedLevel, setSelectedLevel] = useState(0);
  const [menuItems, setMenuItems] = useState({ items: [] });

  useLayoutEffect(() => {
    setMenuItems(menuItem);
    // eslint-disable-next-line
  }, [menuItem]);

  const isHorizontal = menuOrientation === MenuOrientation.HORIZONTAL && !downLG;

  const lastItem = isHorizontal ? HORIZONTAL_MAX_ITEM : null;
  let lastItemIndex = menuItems.items.length - 1;
  let remItems = [];
  let lastItemId;

  //  first it checks menu item is more than giving HORIZONTAL_MAX_ITEM after that get lastItemid by giving horizontal max
  // item and it sets horizontal menu by giving horizontal max item lastly slice menuItem from array and set into remItems

  if (lastItem && lastItem < menuItems.items.length) {
    lastItemId = menuItems.items[lastItem - 1].id;
    lastItemIndex = lastItem - 1;
    remItems = menuItems.items.slice(lastItem - 1, menuItems.items.length).map((item) => ({
      title: item.title,
      elements: item.children,
      icon: item.icon,
      ...(item.url && {
        url: item.url
      })
    }));
  }

  const navGroups = menuItems.items.slice(0, lastItemIndex + 1).map((item, index) => {
    switch (item.type) {
      case 'group':
        if (item.url && item.id !== lastItemId) {
          return (
            <List key={item.id} {...(isHorizontal && { sx: { mt: 0.5 } })}>
              {/* {!isHorizontal && index !== 0 && <Divider sx={{ my: 0.5 }} />} */}
              <NavItem item={item} level={1} isParents />
            </List>
          );
        }

        return (
          <NavGroup
            key={item.id}
            setSelectedID={setSelectedID}
            setSelectedItems={setSelectedItems}
            setSelectedLevel={setSelectedLevel}
            selectedLevel={selectedLevel}
            selectedID={selectedID}
            selectedItems={selectedItems}
            lastItem={lastItem}
            remItems={remItems}
            lastItemId={lastItemId}
            item={item}
          />
        );
      default:
        return (
          <Typography key={item.id} variant="h6" color="error" align="center">
            Fix - Navigation Group
          </Typography>
        );
    }
  });

  return (
    <Box
      sx={{
        pt: drawerOpen ? (isHorizontal ? 0 : 2) : 0,
        ...(!isHorizontal && { '& > ul:first-of-type': { mt: 0 } }),
        display: isHorizontal ? { xs: 'block', lg: 'flex' } : 'block'
      }}
    >
      {navGroups}
    </Box>
  );
}



// import { useLayoutEffect, useState } from 'react';
// import useMediaQuery from '@mui/material/useMediaQuery';
// import Box from '@mui/material/Box';
// import Divider from '@mui/material/Divider';
// import List from '@mui/material/List';
// import Typography from '@mui/material/Typography';

// // Project imports
// import NavItem from './NavItem';
// import NavGroup from './NavGroup';
// import menuItem from 'menu-items';
// import useConfig from 'hooks/useConfig';
// import { HORIZONTAL_MAX_ITEM, MenuOrientation } from 'config';
// import { useGetMenuMaster } from 'api/menu';
// import { useRBAC } from 'pages/permissions/RBACContext';

// // ==============================|| DRAWER CONTENT - NAVIGATION ||============================== //

// export default function Navigation() {
//   const { menuOrientation } = useConfig();
//   const { menuMaster } = useGetMenuMaster();
//   const { canMenuPage } = useRBAC(); // 🔹 Get RBAC function

//   console.log("this is to test the data")
//   const drawerOpen = menuMaster.isDashboardDrawerOpened;
//   const downLG = useMediaQuery((theme) => theme.breakpoints.down('lg'));

//   const [selectedID, setSelectedID] = useState('');
//   const [selectedItems, setSelectedItems] = useState('');
//   const [selectedLevel, setSelectedLevel] = useState(0);
//   const [menuItems, setMenuItems] = useState({ items: [] });

//   // 🔹 Filter menu items based on RBAC permissions
//   useLayoutEffect(() => {
//     const filteredMenu = {
//       items: menuItem.items.filter(item =>
//         canMenuPage(item.access.menu, item.access.page, item.access.requiredPermission)
//       )
//     };

//     setMenuItems(filteredMenu);
//   }, [menuItem, canMenuPage]);

//   const isHorizontal = menuOrientation === MenuOrientation.HORIZONTAL && !downLG;

//   const lastItem = isHorizontal ? HORIZONTAL_MAX_ITEM : null;
//   let lastItemIndex = menuItems.items.length - 1;
//   let remItems = [];
//   let lastItemId;

//   if (lastItem && lastItem < menuItems.items.length) {
//     lastItemId = menuItems.items[lastItem - 1].id;
//     lastItemIndex = lastItem - 1;
//     remItems = menuItems.items.slice(lastItem - 1).map((item) => ({
//       title: item.title,
//       elements: item.children,
//       icon: item.icon,
//       ...(item.url && { url: item.url })
//     }));
//   }

//   // 🔹 Render only items that pass RBAC check
//   const navGroups = menuItems.items.slice(0, lastItemIndex + 1).map((item, index) => {
//     if (!canMenuPage(item.access.menu, item.access.page, item.access.requiredPermission)) {
//       return null; // 🔹 Hide menu items the user does not have access to
//     }

//     switch (item.type) {
//       case 'group':
//         if (item.url && item.id !== lastItemId) {
//           return (
//             <List key={item.id} {...(isHorizontal && { sx: { mt: 2 } })}>
//               <NavItem item={item} level={1} isParents />
//             </List>
//           );
//         }

//         return (
//           <NavGroup
//             key={item.id}
//             setSelectedID={setSelectedID}
//             setSelectedItems={setSelectedItems}
//             setSelectedLevel={setSelectedLevel}
//             selectedLevel={selectedLevel}
//             selectedID={selectedID}
//             selectedItems={selectedItems}
//             lastItem={lastItem}
//             remItems={remItems}
//             lastItemId={lastItemId}
//             item={item}
//           />
//         );
//       default:
//         return (
//           <Typography key={item.id} variant="h6" color="error" align="center">
//             Fix - Navigation Group
//           </Typography>
//         );
//     }
//   });

//   return (
//     <Box
//       sx={{
//         pt: drawerOpen ? (isHorizontal ? 0 : 2) : 0,
//         ...(!isHorizontal && { '& > ul:first-of-type': { mt: 0 } }),
//         display: isHorizontal ? { xs: 'block', lg: 'flex' } : 'block'
//       }}
//     >
//       {navGroups}
//     </Box>
//   );
// }
