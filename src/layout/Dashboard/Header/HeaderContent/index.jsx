import useMediaQuery from '@mui/material/useMediaQuery';
import Box from '@mui/material/Box';

// project-imports
import Search from './Search';
import Message from './Message';
import Profile from './Profile';
import Notification from './Notification';
import MobileSection from './MobileSection';
import FullScreen from './FullScreen';
import MegaMenuSection from './MegaMenuSection';
import CustomAppBar from 'layout/Dashboard/Drawer/HorizontalBar';
import { MenuOrientation } from 'config';
import useConfig from 'hooks/useConfig';
import DrawerHeader from 'layout/Dashboard/Drawer/DrawerHeader';
import { useMemo } from 'react';
import Navigation from 'layout/Dashboard/Drawer/DrawerContent/Navigation';
import { useRBAC } from 'pages/permissions/RBACContext';


import { PERMISSIONS } from 'constants';
import Breadcrumbs from 'components/@extended/Breadcrumbs';
// ==============================|| HEADER - CONTENT ||============================== //

export default function HeaderContent() {

  const { canAccessTopMenu } = useRBAC();

  console.log("🔹 RBAC Hook Loaded:", useRBAC());

  if (!canAccessTopMenu) {
    console.error("❌ ERROR: canAccessTopMenu is not defined in useRBAC!");
    return null;
  }

  // ✅ Check if "Top_Menu" is allowed
  const isTopMenuAllowed = canAccessTopMenu("Top_Menu",PERMISSIONS.READ);


  
  console.log(`🔹 Top_Menu Access: ${isTopMenuAllowed ? "✅ Allowed" : "❌ Denied"}`);

  if (isTopMenuAllowed) {
    // Show menu, or redirect, or render something
    console.log("✅ User is allowed to access Top_Menu");
  } else {
    // Handle unauthorized case
    console.warn("🚫 User is NOT allowed to access Top_Menu");
  }
  const { menuOrientation } = useConfig();
  // const megaMenu = useMemo(() => <MegaMenuSection />, []);
  const megaMenu = useMemo(() => isTopMenuAllowed ? <MegaMenuSection /> : null, [isTopMenuAllowed]);

  const downLG = useMediaQuery((theme) => theme.breakpoints.down('lg'));

  return (
    <>
      {menuOrientation === MenuOrientation.HORIZONTAL && !downLG && <DrawerHeader open={true} />}
      {/* {!downLG && <Search />} */}
      {/* <Box sx={{ display: 'flex', alignItems: 'center' }}>
        <Navigation />
      </Box> */}
      {/* {!downLG && <Breadcrumbs />} */}
      {/* {!downLG && <Box sx={{ width: '100%', ml: { xs: 0, md: 2 } }}>
      </Box> } */}
    

      {downLG && <Box sx={{ flexGrow:1, ml: 1 }} />}
      {!downLG && megaMenu}
      {/* {downLG && <MegaMenuSection />} */}
      {downLG &&  <MegaMenuSection />}
      {/* <Notification /> */}
      {/* <FullScreen /> */}
      {/* <Message /> */}
      {!downLG && <Profile />}
      {downLG && <MobileSection />}
    </>
  );
}
