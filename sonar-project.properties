# SonarQube Configuration for ATS Frontend Application
sonar.projectKey=ats-frontend-application
sonar.projectName=ATS Frontend Application
sonar.projectVersion=9.2.0

# Source code
sonar.sources=src
sonar.exclusions=**/*.test.js,**/*.test.jsx,**/*.spec.js,**/*.spec.jsx,**/test/**/*,**/coverage/**/*,**/node_modules/**/*,**/*.d.ts,**/src/pages/**/*,**/src/pages/*,**/src/pages/*/**/*
sonar.inclusions=src/**/*.js,src/**/*.jsx,src/**/*.ts,src/**/*.tsx,src/pages/client-page/**/*

# Test files
sonar.tests=test
sonar.test.inclusions=**/*.test.js,**/*.test.jsx,**/*.spec.js,**/*.spec.jsx

# Coverage reports
sonar.javascript.lcov.reportPaths=coverage/lcov.info
sonar.coverage.exclusions=**/*.test.js,**/*.test.jsx,**/*.spec.js,**/*.spec.jsx,**/test/**/*,**/coverage/**/*,**/node_modules/**/*,**/src/pages/**/*,**/src/pages/*,**/src/pages/*/**/*

# Language
sonar.language=js

# Encoding
sonar.sourceEncoding=UTF-8

# Quality Gate
sonar.qualitygate.wait=true

# Additional settings
sonar.host.url=http://localhost:9000
# Generate token from SonarQube UI: Administration > Security > Users > Generate Token
# Replace 'your-generated-token-here' with the actual token from SonarQube UI
sonar.token=sqp_f182504535bb863594d1fe3057b373f72a4664d2