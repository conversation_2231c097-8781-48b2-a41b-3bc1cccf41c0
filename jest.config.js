module.exports = {
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/src/setupTests.js', '<rootDir>/test/setup.js'],
  moduleNameMapper: {
    '^src/(.*)$': '<rootDir>/src/$1',
    '^components/(.*)$': '<rootDir>/src/components/$1',
    '^pages/(.*)$': '<rootDir>/src/pages/$1',
    '^utils/(.*)$': '<rootDir>/src/utils/$1',
    '^hooks/(.*)$': '<rootDir>/src/hooks/$1',
    '^contexts/(.*)$': '<rootDir>/src/contexts/$1',
    '^api/(.*)$': '<rootDir>/src/api/$1',
    '^themes/(.*)$': '<rootDir>/src/themes/$1',
    '^menu-items/(.*)$': '<rootDir>/src/menu-items/$1',
    '^custom-components/(.*)$': '<rootDir>/src/custom-components/$1',
    '^constants/(.*)$': '<rootDir>/src/constants/$1',
    '^store/(.*)$': '<rootDir>/src/store/$1',
    '^layout/(.*)$': '<rootDir>/src/layout/$1',
    '^routes/(.*)$': '<rootDir>/src/routes/$1',
    '^sections/(.*)$': '<rootDir>/src/sections/$1',
    '^profile-menu/(.*)$': '<rootDir>/src/profile-menu/$1',
    '^data/(.*)$': '<rootDir>/src/data/$1',
    '^assets/(.*)$': '<rootDir>/src/assets/$1'
  },
  moduleFileExtensions: ['js', 'jsx', 'ts', 'tsx', 'json'],
  transform: {
    '^.+\\.(js|jsx|ts|tsx)$': ['babel-jest', {
      presets: [
        ['@babel/preset-env', { targets: { node: 'current' } }],
        ['@babel/preset-react', { runtime: 'automatic' }]
      ]
    }]
  },
  transformIgnorePatterns: [
    'node_modules/(?!(axios|@mui|@emotion|@hello-pangea|iconsax-react|lucide-react)/)'
  ],
  testMatch: [
    '<rootDir>/test/**/*.test.{js,jsx,ts,tsx}',
    '<rootDir>/test/**/*.spec.{js,jsx,ts,tsx}'
  ],
  collectCoverageFrom: [
    'src/**/*.{js,jsx,ts,tsx}',
    '!src/**/*.d.ts',
    '!src/index.jsx',
    '!src/reportWebVitals.js',
    '!src/setupTests.js'
  ],
  coverageReporters: ['text', 'lcov', 'html'],
  coverageDirectory: 'coverage',
  testTimeout: 10000,
  clearMocks: true,
  restoreMocks: true,
  resetMocks: true,
  globals: {
    'import.meta': {
      env: {
        VITE_APP_API_URL: 'http://localhost:8000'
      }
    }
  }
};
