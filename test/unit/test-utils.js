import React from 'react';
import { render } from '@testing-library/react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react-router-dom';
import { ThemeProvider } from '@mui/material/styles';
import { createTheme } from '@mui/material/styles';
import { JWTContext } from 'contexts/JWTContext';
import { ConfigContext } from 'contexts/ConfigContext';
import axios from 'axios';

// Create a basic theme for testing
const theme = createTheme({
  palette: {
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
  },
});

// Mock user context
const mockUser = {
  id: 1,
  full_name: 'Test User',
  email: '<EMAIL>',
  role: 'admin'
};

// Mock JWT context
const mockJWTContext = {
  user: mockUser,
  isLoggedIn: true,
  login: jest.fn(),
  logout: jest.fn(),
  register: jest.fn(),
  resetPassword: jest.fn(),
  updateProfile: jest.fn()
};

// Mock config context
const mockConfigContext = {
  mode: 'light',
  presetColor: 'default',
  themeDirection: 'ltr',
  menuOrientation: 'vertical',
  onChangeMode: jest.fn(),
  onChangePresetColor: jest.fn(),
  onChangeDirection: jest.fn(),
  onChangeMenuOrientation: jest.fn()
};

// Custom render function that includes providers
const customRender = (ui, options = {}) => {
  const {
    jwtContextValue = mockJWTContext,
    configContextValue = mockConfigContext,
    ...renderOptions
  } = options;

  const Wrapper = ({ children }) => (
    <BrowserRouter>
      <ThemeProvider theme={theme}>
        <ConfigContext.Provider value={configContextValue}>
          <JWTContext.Provider value={jwtContextValue}>
            {children}
          </JWTContext.Provider>
        </ConfigContext.Provider>
      </ThemeProvider>
    </BrowserRouter>
  );

  return render(ui, { wrapper: Wrapper, ...renderOptions });
};

// Mock axios - Enhanced for real component testing
export const mockAxios = {
  get: jest.fn(() => Promise.resolve({ data: {} })),
  post: jest.fn(() => Promise.resolve({ data: {} })),
  put: jest.fn(() => Promise.resolve({ data: {} })),
  delete: jest.fn(() => Promise.resolve({ data: {} })),
  patch: jest.fn(() => Promise.resolve({ data: {} })),
  create: jest.fn(() => ({
    get: jest.fn(() => Promise.resolve({ data: {} })),
    post: jest.fn(() => Promise.resolve({ data: {} })),
    put: jest.fn(() => Promise.resolve({ data: {} })),
    delete: jest.fn(() => Promise.resolve({ data: {} })),
    patch: jest.fn(() => Promise.resolve({ data: {} })),
    interceptors: {
      request: { use: jest.fn(), eject: jest.fn() },
      response: { use: jest.fn(), eject: jest.fn() }
    }
  })),
  // Mock axios instance methods
  mockResolvedValue: jest.fn(),
  mockRejectedValue: jest.fn(),
  mockImplementation: jest.fn()
};

// Setup axios mock for real component testing
jest.mock('axios', () => mockAxios);

// Mock localStorage
export const mockLocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn()
};

// Mock navigate function
export const mockNavigate = jest.fn();

// Mock location
export const mockLocation = {
  pathname: '/test',
  search: '',
  hash: '',
  state: null
};

// Common test data
export const mockClientData = {
  id: 1,
  client_name: 'Test Client',
  email_id: '<EMAIL>',
  contacts_number: '1234567890',
  website: 'https://testclient.com',
  industry: 'Technology',
  country: 'USA',
  state: 'California',
  city: 'San Francisco',
  business_unit: 'IT',
  category: 'Enterprise',
  postal: '94105',
  created_by: 'admin',
  created_on: '2024-01-01'
};

export const mockClientList = [
  mockClientData,
  {
    id: 2,
    client_name: 'Another Client',
    email_id: '<EMAIL>',
    contacts_number: '0987654321',
    website: 'https://anotherclient.com',
    industry: 'Finance',
    country: 'USA',
    state: 'New York',
    city: 'New York',
    business_unit: 'Finance',
    category: 'SMB',
    postal: '10001',
    created_by: 'admin',
    created_on: '2024-01-02'
  }
];

// Export everything
export * from '@testing-library/react';
export { customRender as render };
export { mockUser, mockJWTContext, mockConfigContext, theme };
