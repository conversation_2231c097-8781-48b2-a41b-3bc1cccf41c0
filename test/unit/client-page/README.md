# Client Page Test Suite

This directory contains comprehensive unit tests for all components in the client-page folder structure.

## Test Structure

```
test/unit/client-page/
├── README.md                           # This file
├── client-page.test.jsx                # Integration tests for all components
├── deleteBusinessInformation.test.jsx   # Tests for delete business info dialog
├── deleteClientContactInfo.test.jsx    # Tests for delete contact info dialog
├── index.test.jsx                      # Tests for main client page index
├── ViewFile.test.jsx                   # Tests for file viewer component
├── Dilognew.test.jsx                   # Tests for form dialog component
├── add/
│   └── add.test.jsx                    # Tests for all add components
├── view/
│   └── view.test.jsx                   # Tests for all view components
└── edit/
    └── edit.test.jsx                   # Tests for all edit components
```

## Test Coverage

### Individual Component Tests

#### 1. deleteBusinessInformation.test.jsx
- **Component**: `deleteBusinessInformation.jsx`
- **Purpose**: Confirmation dialog for business information deletion
- **Test Coverage**:
  - Rendering with different states (open/closed)
  - User interactions (cancel/confirm)
  - Accessibility features
  - Edge cases and error handling

#### 2. deleteClientContactInfo.test.jsx
- **Component**: `deleteClientContactInfo.jsx`
- **Purpose**: Confirmation dialog for contact information deletion
- **Test Coverage**:
  - Rendering with different states
  - User interactions
  - Contact-specific functionality
  - Accessibility and error handling

#### 3. index.test.jsx
- **Component**: `index.jsx`
- **Purpose**: Main client page with table and CRUD operations
- **Test Coverage**:
  - Initial rendering and loading states
  - Data fetching and display
  - Search functionality
  - Add/Edit/Delete operations
  - Error handling and API integration

#### 4. ViewFile.test.jsx
- **Component**: `ViewFile.jsx`
- **Purpose**: File preview and display component
- **Test Coverage**:
  - File data display
  - Preview functionality
  - Fullscreen mode
  - Different file types
  - Error handling

#### 5. Dilognew.test.jsx
- **Component**: `Dilognew.jsx`
- **Purpose**: Form dialog for adding/editing clients
- **Test Coverage**:
  - Add mode functionality
  - Edit mode functionality
  - Form validation
  - API integration
  - Error handling

### Folder-based Tests

#### 6. add/add.test.jsx
Tests for all components in the `add/` folder:
- **AddAccount**: Account creation form
- **AddBusinessInfo**: Business information form
- **AddContactNew**: Contact creation form
- **AddDocumentNew**: Document upload form
- **AddGuideLines**: Guidelines creation form
- **AddNotesNew**: Notes creation form

**Test Coverage**:
- Form rendering and validation
- API integration
- Success/error handling
- User interactions

#### 7. view/view.test.jsx
Tests for all components in the `view/` folder:
- **ViewAccount**: Account display component
- **ViewBusinessInfo**: Business info display
- **ViewContact**: Contact list display
- **ViewDocument**: Document list with download
- **ViewGuideLines**: Guidelines display
- **ViewNotes**: Notes display
- **ViewClientNav**: Client navigation

**Test Coverage**:
- Data fetching and display
- Loading states
- Error handling
- Navigation functionality

#### 8. edit/edit.test.jsx
Tests for all components in the `edit/` folder:
- **EditAccountNew**: Account editing form
- **EditContactNew**: Contact editing form
- **EditDocumentNew**: Document editing form
- **EditGuideLines**: Guidelines editing form
- **EditNotesNew**: Notes editing form

**Test Coverage**:
- Data fetching and form population
- Form submission and updates
- API integration
- Error handling

### Integration Tests

#### 9. client-page.test.jsx
Comprehensive integration tests covering:
- **Component Integration**: How all components work together
- **Data Flow Testing**: State management and data propagation
- **User Interaction Flow**: Complete user workflows
- **Performance Testing**: Large dataset handling
- **Accessibility Integration**: Cross-component accessibility
- **Error Boundary Testing**: Error handling across components
- **State Management Testing**: Complex state transitions
- **API Integration Testing**: All API endpoints
- **Cross-Component Communication**: Parent-child data flow
- **Memory Leak Testing**: Resource management

## Test Utilities

The test suite uses a comprehensive set of utilities defined in `../../test-utils.js`:

### Mock Data
- `mockClientData`: Sample client data
- `mockClientList`: Array of sample clients
- `mockUser`: Sample user data

### Mock Services
- `mockAxios`: Mocked HTTP client
- `mockLocalStorage`: Mocked browser storage
- `mockNavigate`: Mocked navigation function

### Custom Render Function
- `customRender`: Enhanced render function with providers
- Includes theme, routing, and context providers

## Running Tests

### Run All Tests
```bash
npm test test/unit/client-page/
```

### Run Specific Test Files
```bash
# Run only dialog tests
npm test test/unit/client-page/deleteBusinessInformation.test.jsx

# Run only add component tests
npm test test/unit/client-page/add/add.test.jsx

# Run only view component tests
npm test test/unit/client-page/view/view.test.jsx
```

### Run with Coverage
```bash
npm test -- --coverage --collectCoverageFrom="src/pages/client-page/**/*.jsx"
```

## Test Categories

### 1. Rendering Tests
- Component renders correctly
- Proper display of data
- Loading states
- Error states

### 2. User Interaction Tests
- Button clicks
- Form submissions
- Input changes
- Navigation

### 3. API Integration Tests
- Data fetching
- Data submission
- Error handling
- Authentication

### 4. Accessibility Tests
- Keyboard navigation
- Screen reader compatibility
- Proper ARIA labels
- Focus management

### 5. Error Handling Tests
- API errors
- Network failures
- Invalid data
- Component errors

### 6. Performance Tests
- Large datasets
- Memory usage
- Re-render optimization
- Resource cleanup

## Best Practices

### 1. Test Organization
- Group related tests in describe blocks
- Use descriptive test names
- Follow AAA pattern (Arrange, Act, Assert)

### 2. Mocking Strategy
- Mock external dependencies
- Use realistic mock data
- Reset mocks between tests

### 3. Async Testing
- Use `waitFor` for async operations
- Handle loading states
- Test error scenarios

### 4. User Experience Testing
- Test complete user workflows
- Verify state transitions
- Check accessibility features

## Coverage Goals

- **Statements**: >90%
- **Branches**: >85%
- **Functions**: >90%
- **Lines**: >90%

## Maintenance

### Adding New Tests
1. Create test file in appropriate directory
2. Follow existing naming conventions
3. Use provided test utilities
4. Add comprehensive coverage

### Updating Tests
1. Update mocks when components change
2. Maintain test data consistency
3. Update integration tests for new features
4. Verify all tests pass

### Debugging Tests
1. Use `console.log` for debugging
2. Check mock implementations
3. Verify async operations
4. Review test data

## Dependencies

- `@testing-library/react`: React testing utilities
- `@testing-library/user-event`: User interaction simulation
- `@testing-library/jest-dom`: Custom matchers
- `jest`: Test runner
- `axios`: HTTP client (mocked)

## Notes

- All tests use mocked components to avoid external dependencies
- Tests focus on behavior rather than implementation details
- Integration tests ensure components work together correctly
- Performance tests help identify bottlenecks
- Accessibility tests ensure inclusive design 