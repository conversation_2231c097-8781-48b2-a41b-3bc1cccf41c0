import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import axios from 'axios';

// Mock axios
jest.mock('axios');
const mockedAxios = axios;

// Simple mock components to avoid jest.mock issues
jest.mock('../../../../src/pages/client-page/edit/accounts/EditAccountNew.jsx', () => {
  return function EditAccountNew({ accountId, onSuccess }) {
    return React.createElement('div', { 'data-testid': 'edit-account-component' },
      React.createElement('h3', null, 'Edit Account'),
      React.createElement('button', {
        'data-testid': 'submit-edit-account',
        onClick: () => onSuccess && onSuccess({ id: accountId })
      }, 'Update Account')
    );
  };
});

jest.mock('../../../../src/pages/client-page/edit/contact/EditContactNew.jsx', () => {
  return function EditContactNew({ contactId, onSuccess }) {
    return React.createElement('div', { 'data-testid': 'edit-contact-component' },
      React.createElement('h3', null, 'Edit Contact'),
      React.createElement('button', {
        'data-testid': 'submit-edit-contact',
        onClick: () => onSuccess && onSuccess({ id: contactId })
      }, 'Update Contact')
    );
  };
});

jest.mock('../../../../src/pages/client-page/edit/documents/EditDocumentNew.jsx', () => {
  return function EditDocumentNew({ documentId, onSuccess }) {
    return React.createElement('div', { 'data-testid': 'edit-document-component' },
      React.createElement('h3', null, 'Edit Document'),
      React.createElement('button', {
        'data-testid': 'submit-edit-document',
        onClick: () => onSuccess && onSuccess({ id: documentId })
      }, 'Update Document')
    );
  };
});

jest.mock('../../../../src/pages/client-page/edit/guidelines/EditGuideLines.jsx', () => {
  return function EditGuideLines({ guidelineId, onSuccess }) {
    return React.createElement('div', { 'data-testid': 'edit-guideline-component' },
      React.createElement('h3', null, 'Edit Guideline'),
      React.createElement('button', {
        'data-testid': 'submit-edit-guideline',
        onClick: () => onSuccess && onSuccess({ id: guidelineId })
      }, 'Update Guideline')
    );
  };
});

jest.mock('../../../../src/pages/client-page/edit/guidelines/EditGuideLines.jsx', () => {
  const mockReact = require('react');
  return function EditGuideLines({ guidelineId, onSuccess }) {
    const [formData, setFormData] = mockReact.useState({
      guideline_title: '',
      guideline_content: '',
      category: '',
      priority: 'medium'
    });

    mockReact.useEffect(() => {
      const fetchGuideline = async () => {
        try {
          const axios = require('axios');
          const response = await axios.get(`/guidelines/${guidelineId}`);
          setFormData(response.data);
        } catch (error) {
          console.error('Error fetching guideline:', error);
        }
      };
      fetchGuideline();
    }, [guidelineId]);

    const handleSubmit = async (e) => {
      e.preventDefault();
      try {
        const axios = require('axios');
        const response = await axios.put(`/guidelines/${guidelineId}`, formData);
        onSuccess(response.data);
      } catch (error) {
        console.error('Error updating guideline:', error);
      }
    };

    return mockReact.createElement('div', { 'data-testid': 'edit-guideline-component' },
      mockReact.createElement('h3', null, 'Edit Guideline')
        <form onSubmit={handleSubmit}>
          <input
            data-testid="guideline-title-input"
            type="text"
            value={formData.guideline_title}
            onChange={(e) => setFormData(prev => ({ ...prev, guideline_title: e.target.value }))}
            required
          />
          <button data-testid="submit-edit-guideline" type="submit">Update Guideline</button>
        </form>
      </div>
    );
  };
});

jest.mock('../../../../src/pages/client-page/edit/notes/EditNotesNew.jsx', () => {
  return function EditNotesNew({ noteId, onSuccess }) {
    const [formData, setFormData] = React.useState({
      note_title: '',
      note_content: '',
      note_type: 'general'
    });

    React.useEffect(() => {
      const fetchNote = async () => {
        try {
          const response = await mockAxios.get(`/notes/${noteId}`);
          setFormData(response.data);
        } catch (error) {
          console.error('Error fetching note:', error);
        }
      };
      fetchNote();
    }, [noteId]);

    const handleSubmit = async (e) => {
      e.preventDefault();
      try {
        const response = await mockAxios.put(`/notes/${noteId}`, formData);
        onSuccess(response.data);
      } catch (error) {
        console.error('Error updating note:', error);
      }
    };

    return (
      <div data-testid="edit-note-component">
        <h3>Edit Note</h3>
        <form onSubmit={handleSubmit}>
          <input
            data-testid="note-title-input"
            type="text"
            value={formData.note_title}
            onChange={(e) => setFormData(prev => ({ ...prev, note_title: e.target.value }))}
            required
          />
          <button data-testid="submit-edit-note" type="submit">Update Note</button>
        </form>
      </div>
    );
  };
});

describe('Edit Components', () => {
  const mockOnSuccess = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('EditAccountNew Component', () => {
    test('should render edit account form', () => {
      customRender(<EditAccountNewComponent accountId={1} onSuccess={mockOnSuccess} />);
      
      expect(screen.getByTestId('edit-account-component')).toBeInTheDocument();
      expect(screen.getByText('Edit Account')).toBeInTheDocument();
    });

    test('should fetch and populate account data', async () => {
      const mockAccount = {
        account_name: 'Test Account',
        account_number: '**********',
        bank_name: 'Test Bank',
        ifsc_code: 'TEST0001234',
        branch_name: 'Test Branch'
      };

      mockAxios.get.mockResolvedValueOnce({ data: mockAccount });

      customRender(<EditAccountNewComponent accountId={1} onSuccess={mockOnSuccess} />);

      await waitFor(() => {
        expect(mockAxios.get).toHaveBeenCalledWith('/accounts/1');
      });
    });

    test('should handle form submission', async () => {
      mockAxios.get.mockResolvedValueOnce({ data: { account_name: 'Test Account' } });
      mockAxios.put.mockResolvedValueOnce({ data: { id: 1, account_name: 'Updated Account' } });

      const user = userEvent.setup();
      customRender(<EditAccountNewComponent accountId={1} onSuccess={mockOnSuccess} />);

      await waitFor(() => {
        expect(screen.getByTestId('account-name-input')).toBeInTheDocument();
      });

      await user.click(screen.getByTestId('submit-edit-account'));

      await waitFor(() => {
        expect(mockAxios.put).toHaveBeenCalledWith('/accounts/1', expect.any(Object));
      });
    });
  });

  describe('EditContactNew Component', () => {
    test('should render edit contact form', () => {
      customRender(<EditContactNewComponent contactId={1} onSuccess={mockOnSuccess} />);
      
      expect(screen.getByTestId('edit-contact-component')).toBeInTheDocument();
      expect(screen.getByText('Edit Contact')).toBeInTheDocument();
    });

    test('should handle form submission', async () => {
      mockAxios.get.mockResolvedValueOnce({ data: { contact_name: 'John Doe' } });
      mockAxios.put.mockResolvedValueOnce({ data: { id: 1, contact_name: 'Updated Contact' } });

      const user = userEvent.setup();
      customRender(<EditContactNewComponent contactId={1} onSuccess={mockOnSuccess} />);

      await waitFor(() => {
        expect(screen.getByTestId('contact-name-input')).toBeInTheDocument();
      });

      await user.click(screen.getByTestId('submit-edit-contact'));

      await waitFor(() => {
        expect(mockAxios.put).toHaveBeenCalledWith('/contacts/1', expect.any(Object));
      });
    });
  });

  describe('EditDocumentNew Component', () => {
    test('should render edit document form', () => {
      customRender(<EditDocumentNewComponent documentId={1} onSuccess={mockOnSuccess} />);
      
      expect(screen.getByTestId('edit-document-component')).toBeInTheDocument();
      expect(screen.getByText('Edit Document')).toBeInTheDocument();
    });

    test('should handle form submission', async () => {
      mockAxios.get.mockResolvedValueOnce({ data: { document_name: 'Test Document' } });
      mockAxios.put.mockResolvedValueOnce({ data: { id: 1, document_name: 'Updated Document' } });

      const user = userEvent.setup();
      customRender(<EditDocumentNewComponent documentId={1} onSuccess={mockOnSuccess} />);

      await waitFor(() => {
        expect(screen.getByTestId('document-name-input')).toBeInTheDocument();
      });

      await user.click(screen.getByTestId('submit-edit-document'));

      await waitFor(() => {
        expect(mockAxios.put).toHaveBeenCalledWith('/documents/1', expect.any(Object));
      });
    });
  });

  describe('EditGuideLines Component', () => {
    test('should render edit guideline form', () => {
      customRender(<EditGuideLinesComponent guidelineId={1} onSuccess={mockOnSuccess} />);
      
      expect(screen.getByTestId('edit-guideline-component')).toBeInTheDocument();
      expect(screen.getByText('Edit Guideline')).toBeInTheDocument();
    });

    test('should handle form submission', async () => {
      mockAxios.get.mockResolvedValueOnce({ data: { guideline_title: 'Test Guideline' } });
      mockAxios.put.mockResolvedValueOnce({ data: { id: 1, guideline_title: 'Updated Guideline' } });

      const user = userEvent.setup();
      customRender(<EditGuideLinesComponent guidelineId={1} onSuccess={mockOnSuccess} />);

      await waitFor(() => {
        expect(screen.getByTestId('guideline-title-input')).toBeInTheDocument();
      });

      await user.click(screen.getByTestId('submit-edit-guideline'));

      await waitFor(() => {
        expect(mockAxios.put).toHaveBeenCalledWith('/guidelines/1', expect.any(Object));
      });
    });
  });

  describe('EditNotesNew Component', () => {
    test('should render edit note form', () => {
      customRender(<EditNotesNewComponent noteId={1} onSuccess={mockOnSuccess} />);
      
      expect(screen.getByTestId('edit-note-component')).toBeInTheDocument();
      expect(screen.getByText('Edit Note')).toBeInTheDocument();
    });

    test('should handle form submission', async () => {
      mockAxios.get.mockResolvedValueOnce({ data: { note_title: 'Test Note' } });
      mockAxios.put.mockResolvedValueOnce({ data: { id: 1, note_title: 'Updated Note' } });

      const user = userEvent.setup();
      customRender(<EditNotesNewComponent noteId={1} onSuccess={mockOnSuccess} />);

      await waitFor(() => {
        expect(screen.getByTestId('note-title-input')).toBeInTheDocument();
      });

      await user.click(screen.getByTestId('submit-edit-note'));

      await waitFor(() => {
        expect(mockAxios.put).toHaveBeenCalledWith('/notes/1', expect.any(Object));
      });
    });
  });

  describe('Error Handling', () => {
    test('should handle API errors gracefully', async () => {
      mockAxios.get.mockRejectedValueOnce(new Error('API Error'));

      customRender(<EditAccountNewComponent accountId={1} onSuccess={mockOnSuccess} />);

      await waitFor(() => {
        expect(screen.getByTestId('edit-account-component')).toBeInTheDocument();
      });
    });
  });

  describe('Integration with Parent Component', () => {
    test('should call onSuccess after successful update', async () => {
      const mockResponseData = { id: 1, account_name: 'Updated Account' };
      mockAxios.get.mockResolvedValueOnce({ data: { account_name: 'Test Account' } });
      mockAxios.put.mockResolvedValueOnce({ data: mockResponseData });

      const user = userEvent.setup();
      const onSuccess = jest.fn();

      customRender(<EditAccountNewComponent accountId={1} onSuccess={onSuccess} />);

      await waitFor(() => {
        expect(screen.getByTestId('account-name-input')).toBeInTheDocument();
      });

      await user.click(screen.getByTestId('submit-edit-account'));

      await waitFor(() => {
        expect(onSuccess).toHaveBeenCalledWith(mockResponseData);
      });
    });
  });
}); 