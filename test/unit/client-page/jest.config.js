module.exports = {
  // Test environment
  testEnvironment: 'jsdom',
  
  // Setup files
  setupFilesAfterEnv: ['<rootDir>/setup.js'],
  
  // Test file patterns
  testMatch: [
    '<rootDir>/unit/client-page/**/*.test.jsx',
    '<rootDir>/unit/client-page/**/*.test.js'
  ],
  
  // Coverage configuration
  collectCoverageFrom: [
    'src/pages/client-page/**/*.jsx',
    'src/pages/client-page/**/*.js',
    '!src/pages/client-page/**/*.test.jsx',
    '!src/pages/client-page/**/*.test.js'
  ],
  
  // Coverage thresholds
  coverageThreshold: {
    global: {
      branches: 85,
      functions: 90,
      lines: 90,
      statements: 90
    }
  },
  
  // Coverage reporters
  coverageReporters: [
    'text',
    'lcov',
    'html'
  ],
  
  // Module name mapping
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '^components/(.*)$': '<rootDir>/src/components/$1',
    '^pages/(.*)$': '<rootDir>/src/pages/$1',
    '^utils/(.*)$': '<rootDir>/src/utils/$1',
    '^api/(.*)$': '<rootDir>/src/api/$1',
    '^contexts/(.*)$': '<rootDir>/src/contexts/$1'
  },
  
  // Transform configuration
  transform: {
    '^.+\\.(js|jsx)$': 'babel-jest'
  },
  
  // Transform ignore patterns
  transformIgnorePatterns: [
    'node_modules/(?!(axios|@mui|react-router-dom)/)'
  ],
  
  // Module file extensions
  moduleFileExtensions: [
    'js',
    'jsx',
    'json'
  ],
  
  // Test timeout
  testTimeout: 10000,
  
  // Verbose output
  verbose: true,
  
  // Clear mocks between tests
  clearMocks: true,
  
  // Restore mocks between tests
  restoreMocks: true,
  
  // Reset modules between tests
  resetModules: true,
  
  // Collect coverage
  collectCoverage: true,
  
  // Coverage directory
  coverageDirectory: 'coverage/client-page',
  
  // Coverage exclude patterns
  coveragePathIgnorePatterns: [
    '/node_modules/',
    '/test/',
    '/coverage/',
    '/dist/',
    '/build/'
  ],
  
  // Test results processor
  testResultsProcessor: 'jest-junit',
  
  // Reporters
  reporters: [
    'default',
    [
      'jest-junit',
      {
        outputDirectory: 'test-results',
        outputName: 'client-page-test-results.xml',
        classNameTemplate: '{classname}',
        titleTemplate: '{title}',
        ancestorSeparator: ' › ',
        usePathForSuiteName: true
      }
    ]
  ],
  
  // Global setup
  globalSetup: '<rootDir>/unit/client-page/global-setup.js',
  
  // Global teardown
  globalTeardown: '<rootDir>/unit/client-page/global-teardown.js',
  
  // Test environment options
  testEnvironmentOptions: {
    url: 'http://localhost:3000'
  },
  
  // Module paths
  modulePaths: [
    '<rootDir>/src',
    '<rootDir>/node_modules'
  ],
  
  // Roots
  roots: [
    '<rootDir>/src',
    '<rootDir>/test'
  ],
  
  // Snapshot serializers
  snapshotSerializers: [
    'enzyme-to-json/serializer'
  ],
  
  // Watch plugins
  watchPlugins: [
    'jest-watch-typeahead/filename',
    'jest-watch-typeahead/testname'
  ],
  
  // Notify mode
  notifyMode: 'always',
  
  // Error on coverage
  errorOnDeprecated: true,
  
  // Force exit
  forceExit: true,
  
  // Detect open handles
  detectOpenHandles: true,
  
  // Run tests in band
  runInBand: false,
  
  // Max workers
  maxWorkers: '50%',
  
  // Cache
  cache: true,
  
  // Cache directory
  cacheDirectory: '<rootDir>/.jest-cache',
  
  // Cache key
  cacheKey: 'client-page-tests',
  
  // Prettier ignore
  prettierPath: null,
  
  // Projects
  projects: [
    {
      displayName: 'client-page',
      testMatch: [
        '<rootDir>/unit/client-page/**/*.test.jsx'
      ],
      setupFilesAfterEnv: [
        '<rootDir>/unit/test-utils.js'
      ]
    }
  ]
}; 