import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { render as customRender, mockAxios, mockClientData } from '../../../test-utils';

// Mock all view components
jest.mock('../../../../src/pages/client-page/view/account/ViewAccount.jsx', () => {
  return function ViewAccount({ clientId }) {
    const [accounts, setAccounts] = React.useState([]);
    const [loading, setLoading] = React.useState(true);

    React.useEffect(() => {
      const fetchAccounts = async () => {
        try {
          const response = await mockAxios.get(`/clients/${clientId}/accounts`);
          setAccounts(response.data);
        } catch (error) {
          console.error('Error fetching accounts:', error);
        } finally {
          setLoading(false);
        }
      };
      fetchAccounts();
    }, [clientId]);

    if (loading) {
      return <div data-testid="loading-accounts">Loading accounts...</div>;
    }

    return (
      <div data-testid="view-accounts-component">
        <h3>Client Accounts</h3>
        <div data-testid="accounts-list">
          {accounts.map((account) => (
            <div key={account.id} data-testid={`account-${account.id}`}>
              <h4>{account.account_name}</h4>
              <p>Account Number: {account.account_number}</p>
              <p>Bank: {account.bank_name}</p>
              <p>IFSC: {account.ifsc_code}</p>
              <p>Branch: {account.branch_name}</p>
            </div>
          ))}
        </div>
      </div>
    );
  };
});

jest.mock('../../../../src/pages/client-page/view/businessinfo/ViewBusinessInfo.jsx', () => {
  return function ViewBusinessInfo({ clientId }) {
    const [businessInfo, setBusinessInfo] = React.useState(null);
    const [loading, setLoading] = React.useState(true);

    React.useEffect(() => {
      const fetchBusinessInfo = async () => {
        try {
          const response = await mockAxios.get(`/clients/${clientId}/business-info`);
          setBusinessInfo(response.data);
        } catch (error) {
          console.error('Error fetching business info:', error);
        } finally {
          setLoading(false);
        }
      };
      fetchBusinessInfo();
    }, [clientId]);

    if (loading) {
      return <div data-testid="loading-business-info">Loading business info...</div>;
    }

    if (!businessInfo) {
      return <div data-testid="no-business-info">No business information available</div>;
    }

    return (
      <div data-testid="view-business-info-component">
        <h3>Business Information</h3>
        <div data-testid="business-info-details">
          <p>Industry: {businessInfo.industry}</p>
          <p>Company Size: {businessInfo.company_size}</p>
          <p>Annual Revenue: {businessInfo.annual_revenue}</p>
          <p>Website: {businessInfo.website}</p>
          <p>Description: {businessInfo.description}</p>
        </div>
      </div>
    );
  };
});

jest.mock('../../../../src/pages/client-page/view/contact/ViewContact/ViewContact.jsx', () => {
  return function ViewContact({ clientId }) {
    const [contacts, setContacts] = React.useState([]);
    const [loading, setLoading] = React.useState(true);

    React.useEffect(() => {
      const fetchContacts = async () => {
        try {
          const response = await mockAxios.get(`/clients/${clientId}/contacts`);
          setContacts(response.data);
        } catch (error) {
          console.error('Error fetching contacts:', error);
        } finally {
          setLoading(false);
        }
      };
      fetchContacts();
    }, [clientId]);

    if (loading) {
      return <div data-testid="loading-contacts">Loading contacts...</div>;
    }

    return (
      <div data-testid="view-contacts-component">
        <h3>Client Contacts</h3>
        <div data-testid="contacts-list">
          {contacts.map((contact) => (
            <div key={contact.id} data-testid={`contact-${contact.id}`}>
              <h4>{contact.contact_name}</h4>
              <p>Email: {contact.email}</p>
              <p>Phone: {contact.phone}</p>
              <p>Position: {contact.position}</p>
              <p>Department: {contact.department}</p>
            </div>
          ))}
        </div>
      </div>
    );
  };
});

jest.mock('../../../../src/pages/client-page/view/documents/ViewDocument/ViewDocument.jsx', () => {
  return function ViewDocument({ clientId }) {
    const [documents, setDocuments] = React.useState([]);
    const [loading, setLoading] = React.useState(true);

    React.useEffect(() => {
      const fetchDocuments = async () => {
        try {
          const response = await mockAxios.get(`/clients/${clientId}/documents`);
          setDocuments(response.data);
        } catch (error) {
          console.error('Error fetching documents:', error);
        } finally {
          setLoading(false);
        }
      };
      fetchDocuments();
    }, [clientId]);

    if (loading) {
      return <div data-testid="loading-documents">Loading documents...</div>;
    }

    return (
      <div data-testid="view-documents-component">
        <h3>Client Documents</h3>
        <div data-testid="documents-list">
          {documents.map((document) => (
            <div key={document.id} data-testid={`document-${document.id}`}>
              <h4>{document.document_name}</h4>
              <p>Type: {document.document_type}</p>
              <p>Description: {document.description}</p>
              <button data-testid={`download-${document.id}`}>Download</button>
            </div>
          ))}
        </div>
      </div>
    );
  };
});

jest.mock('../../../../src/pages/client-page/view/guidelines/ViewGuideLines.jsx', () => {
  return function ViewGuideLines({ clientId }) {
    const [guidelines, setGuidelines] = React.useState([]);
    const [loading, setLoading] = React.useState(true);

    React.useEffect(() => {
      const fetchGuidelines = async () => {
        try {
          const response = await mockAxios.get(`/clients/${clientId}/guidelines`);
          setGuidelines(response.data);
        } catch (error) {
          console.error('Error fetching guidelines:', error);
        } finally {
          setLoading(false);
        }
      };
      fetchGuidelines();
    }, [clientId]);

    if (loading) {
      return <div data-testid="loading-guidelines">Loading guidelines...</div>;
    }

    return (
      <div data-testid="view-guidelines-component">
        <h3>Client Guidelines</h3>
        <div data-testid="guidelines-list">
          {guidelines.map((guideline) => (
            <div key={guideline.id} data-testid={`guideline-${guideline.id}`}>
              <h4>{guideline.guideline_title}</h4>
              <p>Content: {guideline.guideline_content}</p>
              <p>Category: {guideline.category}</p>
              <p>Priority: {guideline.priority}</p>
            </div>
          ))}
        </div>
      </div>
    );
  };
});

jest.mock('../../../../src/pages/client-page/view/notes/ViewNotes.jsx', () => {
  return function ViewNotes({ clientId }) {
    const [notes, setNotes] = React.useState([]);
    const [loading, setLoading] = React.useState(true);

    React.useEffect(() => {
      const fetchNotes = async () => {
        try {
          const response = await mockAxios.get(`/clients/${clientId}/notes`);
          setNotes(response.data);
        } catch (error) {
          console.error('Error fetching notes:', error);
        } finally {
          setLoading(false);
        }
      };
      fetchNotes();
    }, [clientId]);

    if (loading) {
      return <div data-testid="loading-notes">Loading notes...</div>;
    }

    return (
      <div data-testid="view-notes-component">
        <h3>Client Notes</h3>
        <div data-testid="notes-list">
          {notes.map((note) => (
            <div key={note.id} data-testid={`note-${note.id}`}>
              <h4>{note.note_title}</h4>
              <p>Content: {note.note_content}</p>
              <p>Type: {note.note_type}</p>
              <p>Created: {note.created_at}</p>
            </div>
          ))}
        </div>
      </div>
    );
  };
});

jest.mock('../../../../src/pages/client-page/view/navigate/ViewClientNav.jsx', () => {
  return function ViewClientNav({ clientId }) {
    const [client, setClient] = React.useState(null);
    const [loading, setLoading] = React.useState(true);

    React.useEffect(() => {
      const fetchClient = async () => {
        try {
          const response = await mockAxios.get(`/clients/${clientId}`);
          setClient(response.data);
        } catch (error) {
          console.error('Error fetching client:', error);
        } finally {
          setLoading(false);
        }
      };
      fetchClient();
    }, [clientId]);

    if (loading) {
      return <div data-testid="loading-client">Loading client...</div>;
    }

    if (!client) {
      return <div data-testid="no-client">Client not found</div>;
    }

    return (
      <div data-testid="view-client-nav-component">
        <h3>Client Navigation</h3>
        <div data-testid="client-details">
          <h4>{client.client_name}</h4>
          <p>Email: {client.email_id}</p>
          <p>Contact: {client.contacts_number}</p>
          <p>Website: {client.website}</p>
          <p>Industry: {client.industry}</p>
          <p>Country: {client.country}</p>
          <p>State: {client.saret}</p>
          <p>City: {client.city}</p>
        </div>
        <nav data-testid="client-nav">
          <button data-testid="nav-accounts">Accounts</button>
          <button data-testid="nav-contacts">Contacts</button>
          <button data-testid="nav-documents">Documents</button>
          <button data-testid="nav-notes">Notes</button>
          <button data-testid="nav-guidelines">Guidelines</button>
        </nav>
      </div>
    );
  };
});

describe('View Components', () => {
  const mockClientId = 1;

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('ViewAccount Component', () => {
    test('should render loading state initially', () => {
      customRender(<ViewAccountComponent clientId={mockClientId} />);
      
      expect(screen.getByTestId('loading-accounts')).toBeInTheDocument();
    });

    test('should display accounts after loading', async () => {
      const mockAccounts = [
        {
          id: 1,
          account_name: 'Main Account',
          account_number: '**********',
          bank_name: 'Test Bank',
          ifsc_code: 'TEST0001234',
          branch_name: 'Main Branch'
        },
        {
          id: 2,
          account_name: 'Secondary Account',
          account_number: '**********',
          bank_name: 'Another Bank',
          ifsc_code: 'ANOT0005678',
          branch_name: 'Secondary Branch'
        }
      ];

      mockAxios.get.mockResolvedValueOnce({ data: mockAccounts });

      customRender(<ViewAccountComponent clientId={mockClientId} />);

      await waitFor(() => {
        expect(screen.getByTestId('view-accounts-component')).toBeInTheDocument();
        expect(screen.getByText('Client Accounts')).toBeInTheDocument();
        expect(screen.getByText('Main Account')).toBeInTheDocument();
        expect(screen.getByText('Secondary Account')).toBeInTheDocument();
      });
    });

    test('should handle API errors gracefully', async () => {
      mockAxios.get.mockRejectedValueOnce(new Error('API Error'));

      customRender(<ViewAccountComponent clientId={mockClientId} />);

      await waitFor(() => {
        expect(screen.getByTestId('view-accounts-component')).toBeInTheDocument();
      });
    });
  });

  describe('ViewBusinessInfo Component', () => {
    test('should render loading state initially', () => {
      customRender(<ViewBusinessInfoComponent clientId={mockClientId} />);
      
      expect(screen.getByTestId('loading-business-info')).toBeInTheDocument();
    });

    test('should display business info after loading', async () => {
      const mockBusinessInfo = {
        id: 1,
        industry: 'Technology',
        company_size: '100-500',
        annual_revenue: '10M',
        website: 'https://testcompany.com',
        description: 'A technology company'
      };

      mockAxios.get.mockResolvedValueOnce({ data: mockBusinessInfo });

      customRender(<ViewBusinessInfoComponent clientId={mockClientId} />);

      await waitFor(() => {
        expect(screen.getByTestId('view-business-info-component')).toBeInTheDocument();
        expect(screen.getByText('Business Information')).toBeInTheDocument();
        expect(screen.getByText('Industry: Technology')).toBeInTheDocument();
        expect(screen.getByText('Company Size: 100-500')).toBeInTheDocument();
      });
    });

    test('should display no business info message when data is null', async () => {
      mockAxios.get.mockResolvedValueOnce({ data: null });

      customRender(<ViewBusinessInfoComponent clientId={mockClientId} />);

      await waitFor(() => {
        expect(screen.getByTestId('no-business-info')).toBeInTheDocument();
        expect(screen.getByText('No business information available')).toBeInTheDocument();
      });
    });
  });

  describe('ViewContact Component', () => {
    test('should render loading state initially', () => {
      customRender(<ViewContactComponent clientId={mockClientId} />);
      
      expect(screen.getByTestId('loading-contacts')).toBeInTheDocument();
    });

    test('should display contacts after loading', async () => {
      const mockContacts = [
        {
          id: 1,
          contact_name: 'John Doe',
          email: '<EMAIL>',
          phone: '**********',
          position: 'Manager',
          department: 'Sales'
        },
        {
          id: 2,
          contact_name: 'Jane Smith',
          email: '<EMAIL>',
          phone: '**********',
          position: 'Director',
          department: 'Marketing'
        }
      ];

      mockAxios.get.mockResolvedValueOnce({ data: mockContacts });

      customRender(<ViewContactComponent clientId={mockClientId} />);

      await waitFor(() => {
        expect(screen.getByTestId('view-contacts-component')).toBeInTheDocument();
        expect(screen.getByText('Client Contacts')).toBeInTheDocument();
        expect(screen.getByText('John Doe')).toBeInTheDocument();
        expect(screen.getByText('Jane Smith')).toBeInTheDocument();
      });
    });
  });

  describe('ViewDocument Component', () => {
    test('should render loading state initially', () => {
      customRender(<ViewDocumentComponent clientId={mockClientId} />);
      
      expect(screen.getByTestId('loading-documents')).toBeInTheDocument();
    });

    test('should display documents after loading', async () => {
      const mockDocuments = [
        {
          id: 1,
          document_name: 'Contract.pdf',
          document_type: 'contract',
          description: 'Client contract document'
        },
        {
          id: 2,
          document_name: 'Invoice.pdf',
          document_type: 'invoice',
          description: 'Payment invoice'
        }
      ];

      mockAxios.get.mockResolvedValueOnce({ data: mockDocuments });

      customRender(<ViewDocumentComponent clientId={mockClientId} />);

      await waitFor(() => {
        expect(screen.getByTestId('view-documents-component')).toBeInTheDocument();
        expect(screen.getByText('Client Documents')).toBeInTheDocument();
        expect(screen.getByText('Contract.pdf')).toBeInTheDocument();
        expect(screen.getByText('Invoice.pdf')).toBeInTheDocument();
      });
    });

    test('should render download buttons for documents', async () => {
      const mockDocuments = [
        {
          id: 1,
          document_name: 'Contract.pdf',
          document_type: 'contract',
          description: 'Client contract document'
        }
      ];

      mockAxios.get.mockResolvedValueOnce({ data: mockDocuments });

      customRender(<ViewDocumentComponent clientId={mockClientId} />);

      await waitFor(() => {
        expect(screen.getByTestId('download-1')).toBeInTheDocument();
        expect(screen.getByText('Download')).toBeInTheDocument();
      });
    });
  });

  describe('ViewGuideLines Component', () => {
    test('should render loading state initially', () => {
      customRender(<ViewGuideLinesComponent clientId={mockClientId} />);
      
      expect(screen.getByTestId('loading-guidelines')).toBeInTheDocument();
    });

    test('should display guidelines after loading', async () => {
      const mockGuidelines = [
        {
          id: 1,
          guideline_title: 'Communication Protocol',
          guideline_content: 'Follow proper communication guidelines',
          category: 'Process',
          priority: 'high'
        },
        {
          id: 2,
          guideline_title: 'Documentation Standards',
          guideline_content: 'Maintain proper documentation',
          category: 'Quality',
          priority: 'medium'
        }
      ];

      mockAxios.get.mockResolvedValueOnce({ data: mockGuidelines });

      customRender(<ViewGuideLinesComponent clientId={mockClientId} />);

      await waitFor(() => {
        expect(screen.getByTestId('view-guidelines-component')).toBeInTheDocument();
        expect(screen.getByText('Client Guidelines')).toBeInTheDocument();
        expect(screen.getByText('Communication Protocol')).toBeInTheDocument();
        expect(screen.getByText('Documentation Standards')).toBeInTheDocument();
      });
    });
  });

  describe('ViewNotes Component', () => {
    test('should render loading state initially', () => {
      customRender(<ViewNotesComponent clientId={mockClientId} />);
      
      expect(screen.getByTestId('loading-notes')).toBeInTheDocument();
    });

    test('should display notes after loading', async () => {
      const mockNotes = [
        {
          id: 1,
          note_title: 'Initial Meeting',
          note_content: 'Discussed project requirements',
          note_type: 'meeting',
          created_at: '2024-01-01'
        },
        {
          id: 2,
          note_title: 'Follow-up Call',
          note_content: 'Scheduled follow-up meeting',
          note_type: 'follow-up',
          created_at: '2024-01-02'
        }
      ];

      mockAxios.get.mockResolvedValueOnce({ data: mockNotes });

      customRender(<ViewNotesComponent clientId={mockClientId} />);

      await waitFor(() => {
        expect(screen.getByTestId('view-notes-component')).toBeInTheDocument();
        expect(screen.getByText('Client Notes')).toBeInTheDocument();
        expect(screen.getByText('Initial Meeting')).toBeInTheDocument();
        expect(screen.getByText('Follow-up Call')).toBeInTheDocument();
      });
    });
  });

  describe('ViewClientNav Component', () => {
    test('should render loading state initially', () => {
      customRender(<ViewClientNavComponent clientId={mockClientId} />);
      
      expect(screen.getByTestId('loading-client')).toBeInTheDocument();
    });

    test('should display client details after loading', async () => {
      const mockClient = {
        id: 1,
        client_name: 'Test Client',
        email_id: '<EMAIL>',
        contacts_number: '**********',
        website: 'https://testclient.com',
        industry: 'Technology',
        country: 'USA',
        saret: 'California',
        city: 'San Francisco'
      };

      mockAxios.get.mockResolvedValueOnce({ data: mockClient });

      customRender(<ViewClientNavComponent clientId={mockClientId} />);

      await waitFor(() => {
        expect(screen.getByTestId('view-client-nav-component')).toBeInTheDocument();
        expect(screen.getByText('Client Navigation')).toBeInTheDocument();
        expect(screen.getByText('Test Client')).toBeInTheDocument();
        expect(screen.getByText('Email: <EMAIL>')).toBeInTheDocument();
      });
    });

    test('should display navigation buttons', async () => {
      const mockClient = {
        id: 1,
        client_name: 'Test Client',
        email_id: '<EMAIL>',
        contacts_number: '**********',
        website: 'https://testclient.com',
        industry: 'Technology',
        country: 'USA',
        saret: 'California',
        city: 'San Francisco'
      };

      mockAxios.get.mockResolvedValueOnce({ data: mockClient });

      customRender(<ViewClientNavComponent clientId={mockClientId} />);

      await waitFor(() => {
        expect(screen.getByTestId('nav-accounts')).toBeInTheDocument();
        expect(screen.getByTestId('nav-contacts')).toBeInTheDocument();
        expect(screen.getByTestId('nav-documents')).toBeInTheDocument();
        expect(screen.getByTestId('nav-notes')).toBeInTheDocument();
        expect(screen.getByTestId('nav-guidelines')).toBeInTheDocument();
      });
    });

    test('should display no client message when client not found', async () => {
      mockAxios.get.mockResolvedValueOnce({ data: null });

      customRender(<ViewClientNavComponent clientId={mockClientId} />);

      await waitFor(() => {
        expect(screen.getByTestId('no-client')).toBeInTheDocument();
        expect(screen.getByText('Client not found')).toBeInTheDocument();
      });
    });
  });

  describe('Error Handling', () => {
    test('should handle API errors gracefully for all components', async () => {
      mockAxios.get.mockRejectedValue(new Error('API Error'));

      const components = [
        ViewAccountComponent,
        ViewBusinessInfoComponent,
        ViewContactComponent,
        ViewDocumentComponent,
        ViewGuideLinesComponent,
        ViewNotesComponent,
        ViewClientNavComponent
      ];

      for (const Component of components) {
        customRender(<Component clientId={mockClientId} />);
        
        await waitFor(() => {
          expect(screen.getByTestId(`view-${Component.name.toLowerCase().replace('component', '')}-component`)).toBeInTheDocument();
        });
      }
    });
  });

  describe('Data Fetching', () => {
    test('should fetch data with correct client ID', async () => {
      mockAxios.get.mockResolvedValue({ data: [] });

      customRender(<ViewAccountComponent clientId={mockClientId} />);

      await waitFor(() => {
        expect(mockAxios.get).toHaveBeenCalledWith(`/clients/${mockClientId}/accounts`);
      });
    });

    test('should refetch data when client ID changes', async () => {
      mockAxios.get.mockResolvedValue({ data: [] });

      const { rerender } = customRender(<ViewAccountComponent clientId={mockClientId} />);

      await waitFor(() => {
        expect(mockAxios.get).toHaveBeenCalledWith(`/clients/${mockClientId}/accounts`);
      });

      const newClientId = 2;
      rerender(<ViewAccountComponent clientId={newClientId} />);

      await waitFor(() => {
        expect(mockAxios.get).toHaveBeenCalledWith(`/clients/${newClientId}/accounts`);
      });
    });
  });

  describe('Accessibility', () => {
    test('should have proper headings and structure', async () => {
      mockAxios.get.mockResolvedValue({ data: [] });

      customRender(<ViewAccountComponent clientId={mockClientId} />);

      await waitFor(() => {
        expect(screen.getByText('Client Accounts')).toBeInTheDocument();
      });
    });

    test('should have proper navigation structure', async () => {
      const mockClient = {
        id: 1,
        client_name: 'Test Client',
        email_id: '<EMAIL>',
        contacts_number: '**********',
        website: 'https://testclient.com',
        industry: 'Technology',
        country: 'USA',
        saret: 'California',
        city: 'San Francisco'
      };

      mockAxios.get.mockResolvedValue({ data: mockClient });

      customRender(<ViewClientNavComponent clientId={mockClientId} />);

      await waitFor(() => {
        const nav = screen.getByTestId('client-nav');
        expect(nav).toBeInTheDocument();
        expect(nav.querySelectorAll('button')).toHaveLength(5);
      });
    });
  });

  describe('Integration with Parent Component', () => {
    test('should receive and use client ID from parent', async () => {
      const testClientId = 999;
      mockAxios.get.mockResolvedValue({ data: [] });

      customRender(<ViewAccountComponent clientId={testClientId} />);

      await waitFor(() => {
        expect(mockAxios.get).toHaveBeenCalledWith(`/clients/${testClientId}/accounts`);
      });
    });
  });
}); 