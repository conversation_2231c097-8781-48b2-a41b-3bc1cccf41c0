import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { render as customRender, mockAxios } from '../../test-utils';
import ConfirmDeleteDialog from '../../../src/pages/client-page/deleteClientContactInfo';

// Mock the component since it's a simple dialog
jest.mock('../../../src/pages/client-page/deleteClientContactInfo', () => {
  return function ConfirmDeleteDialog({ open, handleClose, handleConfirm, isActive }) {
    if (!open) return null;
    
    return (
      <div data-testid="confirm-delete-contact-dialog">
        <div data-testid="dialog-title">Confirm Status Change</div>
        <div data-testid="dialog-content">
          {isActive 
            ? 'Are you sure you want to mark this contact as inactive?' 
            : 'Are you sure you want to mark this contact as active?'
          }
        </div>
        <div data-testid="dialog-actions">
          <button data-testid="cancel-button" onClick={handleClose}>
            Cancel
          </button>
          <button data-testid="confirm-button" onClick={handleConfirm}>
            Confirm
          </button>
        </div>
      </div>
    );
  };
});

describe('ConfirmDeleteContactDialog Component', () => {
  const defaultProps = {
    open: false,
    handleClose: jest.fn(),
    handleConfirm: jest.fn(),
    isActive: true
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    test('should not render when open is false', () => {
      render(<ConfirmDeleteDialog {...defaultProps} open={false} />);
      expect(screen.queryByTestId('confirm-delete-contact-dialog')).not.toBeInTheDocument();
    });

    test('should render when open is true', () => {
      render(<ConfirmDeleteDialog {...defaultProps} open={true} />);
      expect(screen.getByTestId('confirm-delete-contact-dialog')).toBeInTheDocument();
    });

    test('should display correct title', () => {
      render(<ConfirmDeleteDialog {...defaultProps} open={true} />);
      expect(screen.getByTestId('dialog-title')).toHaveTextContent('Confirm Status Change');
    });

    test('should display correct message for active contact', () => {
      render(<ConfirmDeleteDialog {...defaultProps} open={true} isActive={true} />);
      expect(screen.getByTestId('dialog-content')).toHaveTextContent(
        'Are you sure you want to mark this contact as inactive?'
      );
    });

    test('should display correct message for inactive contact', () => {
      render(<ConfirmDeleteDialog {...defaultProps} open={true} isActive={false} />);
      expect(screen.getByTestId('dialog-content')).toHaveTextContent(
        'Are you sure you want to mark this contact as active?'
      );
    });

    test('should render cancel and confirm buttons', () => {
      render(<ConfirmDeleteDialog {...defaultProps} open={true} />);
      expect(screen.getByTestId('cancel-button')).toBeInTheDocument();
      expect(screen.getByTestId('confirm-button')).toBeInTheDocument();
    });
  });

  describe('User Interactions', () => {
    test('should call handleClose when cancel button is clicked', async () => {
      const handleClose = jest.fn();
      const user = userEvent.setup();
      
      render(<ConfirmDeleteDialog {...defaultProps} open={true} handleClose={handleClose} />);
      
      await user.click(screen.getByTestId('cancel-button'));
      expect(handleClose).toHaveBeenCalledTimes(1);
    });

    test('should call handleConfirm when confirm button is clicked', async () => {
      const handleConfirm = jest.fn();
      const user = userEvent.setup();
      
      render(<ConfirmDeleteDialog {...defaultProps} open={true} handleConfirm={handleConfirm} />);
      
      await user.click(screen.getByTestId('confirm-button'));
      expect(handleConfirm).toHaveBeenCalledTimes(1);
    });

    test('should handle multiple clicks on buttons', async () => {
      const handleClose = jest.fn();
      const handleConfirm = jest.fn();
      const user = userEvent.setup();
      
      render(
        <ConfirmDeleteDialog 
          {...defaultProps} 
          open={true} 
          handleClose={handleClose}
          handleConfirm={handleConfirm}
        />
      );
      
      await user.click(screen.getByTestId('cancel-button'));
      await user.click(screen.getByTestId('confirm-button'));
      await user.click(screen.getByTestId('cancel-button'));
      
      expect(handleClose).toHaveBeenCalledTimes(2);
      expect(handleConfirm).toHaveBeenCalledTimes(1);
    });
  });

  describe('Accessibility', () => {
    test('should have proper button labels', () => {
      render(<ConfirmDeleteDialog {...defaultProps} open={true} />);
      
      const cancelButton = screen.getByTestId('cancel-button');
      const confirmButton = screen.getByTestId('confirm-button');
      
      expect(cancelButton).toHaveTextContent('Cancel');
      expect(confirmButton).toHaveTextContent('Confirm');
    });

    test('should be keyboard accessible', async () => {
      const handleClose = jest.fn();
      const handleConfirm = jest.fn();
      const user = userEvent.setup();
      
      render(
        <ConfirmDeleteDialog 
          {...defaultProps} 
          open={true} 
          handleClose={handleClose}
          handleConfirm={handleConfirm}
        />
      );
      
      // Test tab navigation
      const cancelButton = screen.getByTestId('cancel-button');
      const confirmButton = screen.getByTestId('confirm-button');
      
      cancelButton.focus();
      expect(cancelButton).toHaveFocus();
      
      await user.tab();
      expect(confirmButton).toHaveFocus();
    });
  });

  describe('Edge Cases', () => {
    test('should handle undefined props gracefully', () => {
      render(<ConfirmDeleteDialog open={true} />);
      expect(screen.getByTestId('confirm-delete-contact-dialog')).toBeInTheDocument();
    });

    test('should handle null props gracefully', () => {
      render(<ConfirmDeleteDialog open={true} handleClose={null} handleConfirm={null} />);
      expect(screen.getByTestId('confirm-delete-contact-dialog')).toBeInTheDocument();
    });

    test('should work with different isActive values', () => {
      const { rerender } = render(<ConfirmDeleteDialog {...defaultProps} open={true} isActive={true} />);
      expect(screen.getByTestId('dialog-content')).toHaveTextContent('inactive');
      
      rerender(<ConfirmDeleteDialog {...defaultProps} open={true} isActive={false} />);
      expect(screen.getByTestId('dialog-content')).toHaveTextContent('active');
    });
  });

  describe('Integration with Parent Component', () => {
    test('should integrate with parent component state management', async () => {
      const mockHandleClose = jest.fn();
      const mockHandleConfirm = jest.fn();
      const user = userEvent.setup();
      
      render(
        <ConfirmDeleteDialog 
          {...defaultProps} 
          open={true} 
          handleClose={mockHandleClose}
          handleConfirm={mockHandleConfirm}
        />
      );
      
      // Simulate user interaction
      await user.click(screen.getByTestId('confirm-button'));
      
      expect(mockHandleConfirm).toHaveBeenCalled();
      expect(mockHandleClose).not.toHaveBeenCalled();
    });
  });

  describe('Contact-specific Functionality', () => {
    test('should handle contact status changes correctly', async () => {
      const handleConfirm = jest.fn();
      const user = userEvent.setup();
      
      render(<ConfirmDeleteDialog {...defaultProps} open={true} handleConfirm={handleConfirm} />);
      
      await user.click(screen.getByTestId('confirm-button'));
      
      expect(handleConfirm).toHaveBeenCalled();
    });

    test('should display contact-specific messaging', () => {
      render(<ConfirmDeleteDialog {...defaultProps} open={true} isActive={true} />);
      expect(screen.getByTestId('dialog-content')).toHaveTextContent('contact');
    });
  });
}); 