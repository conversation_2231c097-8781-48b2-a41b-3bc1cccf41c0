// Mock import.meta for Jest
global.importMeta = {
  env: {
    VITE_APP_API_URL: 'http://localhost:8000'
  }
};

// Mock import.meta.env
Object.defineProperty(global, 'import', {
  value: {
    meta: {
      env: {
        VITE_APP_API_URL: 'http://localhost:8000'
      }
    }
  },
  writable: true
});

// Mock environment variables
process.env.VITE_APP_API_URL = 'http://localhost:8000';

// Mock console methods to reduce noise in tests
const originalError = console.error;
const originalWarn = console.warn;
const originalLog = console.log;

beforeEach(() => {
  console.error = jest.fn();
  console.warn = jest.fn();
  console.log = jest.fn();
});

afterEach(() => {
  console.error = originalError;
  console.warn = originalWarn;
  console.log = originalLog;
});
