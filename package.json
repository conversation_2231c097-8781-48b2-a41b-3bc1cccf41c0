{"name": "able-pro-material-react", "version": "9.2.0", "private": true, "dependencies": {"@emotion/cache": "^11.11.0", "@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@fontsource/inter": "^5.0.17", "@fontsource/poppins": "^5.0.13", "@fontsource/public-sans": "^5.0.17", "@fontsource/roboto": "^5.0.12", "@hello-pangea/dnd": "^18.0.1", "@hookform/resolvers": "^3.9.1", "@mui/base": "^5.0.0-beta.40", "@mui/icons-material": "^5.16.7", "@mui/lab": "^5.0.0-alpha.170", "@mui/material": "^5.16.7", "@mui/styles": "^6.0.1", "@mui/system": "^5.16.7", "@mui/x-data-grid": "^7.10.0", "@mui/x-date-pickers": "^7.9.0", "@svgr/webpack": "8.1.0", "@tanstack/match-sorter-utils": "^8.19.4", "@tanstack/react-table": "^8.17.3", "@vitejs/plugin-react": "^4.2.1", "ag-grid-community": "^31.3.2", "ag-grid-react": "^31.3.2", "axios": "^1.6.8", "chance": "^1.1.11", "crypto-js": "^4.2.0", "date-fns": "^3.6.0", "formik": "^2.4.5", "framer-motion": "^11.0.25", "history": "^5.3.0", "iconsax-react": "^0.0.8", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "lucide-react": "^0.511.0", "mammoth": "^1.9.0", "notistack": "^3.0.1", "papaparse": "^5.5.3", "prop-types": "^15.8.1", "react": "^18.2.0", "react-csv": "^2.2.2", "react-device-detect": "^2.2.3", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-draft-wysiwyg": "^1.15.0", "react-dropzone": "^14.2.3", "react-hook-form": "^7.52.1", "react-icons": "^5.2.1", "react-intl": "^6.6.5", "react-number-format": "^5.4.4", "react-router": "^6.22.3", "react-router-dom": "^6.22.3", "react-slick": "^0.30.2", "react-timer-hook": "^3.0.7", "react18-input-otp": "^1.1.4", "simplebar": "^6.2.5", "simplebar-react": "^3.2.4", "slick-carousel": "^1.8.1", "stylis-plugin-rtl": "^2.1.1", "swr": "^2.2.5", "uuid": "^9.0.1", "vite": "^5.2.10", "vite-jsconfig-paths": "^2.0.1", "web-vitals": "^3.5.2", "yup": "^1.4.0"}, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "start:dev": "env-cmd -f .env.development vite", "start:beta": "env-cmd -f .env.beta vite", "start:prod": "env-cmd -f .env.production vite", "build:dev": "env-cmd -f .env.development vite build", "build:beta": "env-cmd -f .env.beta vite build", "build:prod": "env-cmd -f .env.production vite build", "lint": "eslint \"src/**/*.{js,jsx,ts,tsx}\"", "lint:fix": "eslint --fix \"src/**/*.{js,jsx,ts,tsx}\"", "prettier": "prettier --write \"src/**/*.{js,jsx,ts,tsx}\"", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "babel": {"presets": ["@babel/preset-react"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/core": "^7.24.4", "@babel/eslint-parser": "^7.24.1", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "env-cmd": "^10.1.0", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-config-react-app": "7.0.1", "eslint-plugin-flowtype": "^8.0.3", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.34.1", "eslint-plugin-react-hooks": "^4.6.0", "immutable": "^4.3.5", "jest": "^30.0.5", "jest-environment-jsdom": "^30.0.5", "prettier": "^3.2.5", "react-error-overlay": "6.0.11"}, "packageManager": "yarn@4.1.0"}