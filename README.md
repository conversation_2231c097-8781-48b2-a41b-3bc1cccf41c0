# ATS Frontend Application

## Environment Configuration

### Port Configuration
The application uses different ports for different environments:

| Environment | Frontend Port | Backend Port |
|-------------|---------------|--------------|
| Development | 3000          | 3000         |
| Beta        | 3009          | 8094         |
| Production  | 3010          | 8095         |

### Environment Files
Create the following environment files in the root directory:

1. `.env.development`:
```
VITE_APP_API_URL=http://localhost:3000
VITE_APP_PORT=3000
```

2. `.env.beta`:
```
VITE_APP_API_URL=http://localhost:8094
VITE_APP_PORT=3009
```

3. `.env.production`:
```
VITE_APP_API_URL=http://localhost:8095
VITE_APP_PORT=3010
```

## Running the Application

### Development
```bash
npm start
```
- Runs on port 3000
- Connects to backend on port 3000

### Beta Environment
```bash
npm run start:beta
```
- Runs on port 3009
- Connects to backend on port 8094

### Production Environment
```bash
npm run start:production
```
- Runs on port 3010
- Connects to backend on port 8095

## API Configuration

The application uses `VITE_APP_API_URL` for all API calls. This is configured in:

1. `vite.config.mjs` - For proxy configuration
2. `src/utils/axios.js` - For base URL configuration
3. Individual components - For direct API calls

## Project Structure

```
ATS-Frontend-Application/
├── src/
│   ├── pages/           # Page components
│   ├── utils/           # Utility functions
│   │   └── axios.js     # API configuration
│   └── ...
├── public/              # Static files
├── vite.config.mjs      # Vite configuration
├── .env.development     # Development environment
├── .env.beta           # Beta environment
├── .env.production     # Production environment
└── package.json        # Project dependencies
```

## Available Scripts

```bash
# Start development server
npm start

# Start beta environment
npm run start:beta

# Start production environment
npm run start:production

# Build for development
npm run build

# Build for beta
npm run build-beta

# Build for production
npm run build-production

# Lint code
npm run lint

# Fix linting issues
npm run lint:fix

# Format code
npm run prettier
```

## API Integration

The application uses Axios for API calls. The base configuration is in `src/utils/axios.js`:

```javascript
const axiosServices = axios.create({ 
  baseURL: import.meta.env.VITE_APP_API_URL || 'http://localhost:3010/' 
});
```

### API Features
- Automatic token handling
- Error interception
- 401 unauthorized handling
- Request/Response interceptors

## Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| VITE_APP_API_URL | Backend API URL | http://localhost:3010 |
| VITE_APP_PORT | Frontend port | 3000 |
| VITE_APP_BASE_NAME | Application base path | / |

## Deployment

### Development
1. Set up environment variables
2. Run `npm start`

### Beta
1. Set up `.env.beta`
2. Run `npm run start:beta`

### Production
1. Set up `.env.production`
2. Run `npm run start:production`

## Troubleshooting

### Common Issues

1. **Port Already in Use**
   - Check if another instance is running
   - Use `netstat -ano | findstr :<port>` to find processes
   - Kill the process or use a different port

2. **API Connection Issues**
   - Verify environment variables
   - Check backend server status
   - Verify network connectivity

3. **Build Issues**
   - Clear node_modules: `rm -rf node_modules`
   - Clear cache: `npm cache clean --force`
   - Reinstall dependencies: `npm install`

## Support

For any issues or questions:
1. Check the troubleshooting guide
2. Review environment configurations
3. Verify API endpoints
4. Check network connectivity
