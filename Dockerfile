# Step 1: Use Node.js to build the app 
FROM node:18 AS build 
 
# Set working directory 
WORKDIR /app 
 
# Copy dependency files 
COPY package.json yarn.lock ./ 
 
# Install dependencies 
RUN npm install 
 
# Copy the rest of the source code 
COPY . . 
 
# Build the production-ready app 
RUN npm run build 
 
# Step 2: Serve the app using Nginx 
FROM nginx:stable-alpine 
 
# Copy build output to Nginx web root 
COPY --from=build /app/dist /usr/share/nginx/html 
 
# Replace default Nginx config 
COPY nginx.conf /etc/nginx/conf.d/default.conf 
 
# Expose port 80 
EXPOSE 80 
 
# Start Nginx 
CMD ["nginx", "-g", "daemon off;"] 
